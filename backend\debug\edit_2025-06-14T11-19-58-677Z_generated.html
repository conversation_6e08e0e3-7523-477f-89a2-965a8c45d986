<!DOCTYPE html>
<html>
<head>
    <title>Account Management</title>
    <style>
        .hidden {
            display: none;
        }
        .block {
            display: block;
        }
        /* Other existing styles would be here */
    </style>
</head>
<body>
    <div id="app">
        <h1 class="text-xl font-semibold text-gray-900 mr-8" onclick="showAddAccountModal()">Add Account</h1>
    </div>

    <!-- Existing modal (preserved exactly as is) -->
    <div id="addAccountModal" class="hidden">
        <!-- Modal content would be here -->
    </div>

    <script>
        function showAddAccountModal() {
            try {
                const modal = document.getElementById('addAccountModal');
                if (!modal) {
                    throw new Error('Add Account modal not found');
                }
                modal.classList.remove('hidden');
                modal.classList.add('block');
            } catch (error) {
                console.error('Error showing add account modal:', error);
                alert('An error occurred while trying to open the account form. Please try again.');
            }
        }
    </script>