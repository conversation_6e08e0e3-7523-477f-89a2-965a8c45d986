/**
 * Element Selector Service
 * Handles element selection, targeting, and edit mode management
 * Keeps the main editor page clean and modular
 */

import React from 'react';

export interface ElementSelection {
  element: HTMLElement;
  selector: string;
  fragmentHtml: string;
  elementContext: any;
  targetPosition?: 'before' | 'after' | 'inside' | 'replace';
}

export interface EditModeState {
  isActive: boolean;
  selectedElement: ElementSelection | null;
  highlightedElements: HTMLElement[];
}

/**
 * Element Selector Manager
 * Manages element selection and edit mode state
 */
export class ElementSelectorManager {
  private editModeState: EditModeState = {
    isActive: false,
    selectedElement: null,
    highlightedElements: []
  };

  private onSelectionChange?: (selection: ElementSelection | null) => void;
  private onEditModeChange?: (isActive: boolean) => void;
  private iframeRef?: React.RefObject<HTMLIFrameElement>;
  private messageListener?: (event: MessageEvent) => void;
  private containerRef?: React.RefObject<HTMLElement>; // For direct DOM access (SPAShell mode)
  private mode: 'iframe' | 'direct' = 'iframe';

  constructor(
    onSelectionChange?: (selection: ElementSelection | null) => void,
    onEditModeChange?: (isActive: boolean) => void,
    iframeRef?: React.RefObject<HTMLIFrameElement>,
    containerRef?: React.RefObject<HTMLElement>,
    mode: 'iframe' | 'direct' = 'iframe'
  ) {
    this.onSelectionChange = onSelectionChange;
    this.onEditModeChange = onEditModeChange;
    this.iframeRef = iframeRef;
    this.containerRef = containerRef;
    this.mode = mode;
    this.setupMessageListener();
  }

  /**
   * Enter edit mode - enables element selection
   */
  enterEditMode(): void {
    console.log('🎯 [ElementSelector] Entering edit mode');
    this.editModeState.isActive = true;
    this.addElementHighlighting();
    this.onEditModeChange?.(true);
  }

  /**
   * Exit edit mode - disables element selection
   */
  exitEditMode(): void {
    console.log('🎯 [ElementSelector] Exiting edit mode');
    this.editModeState.isActive = false;
    this.clearSelection();
    this.removeElementHighlighting();
    this.onEditModeChange?.(false);
  }

  /**
   * Toggle edit mode
   */
  toggleEditMode(): void {
    if (this.editModeState.isActive) {
      this.exitEditMode();
    } else {
      this.enterEditMode();
    }
  }

  /**
   * Select an element for editing
   */
  selectElement(element: HTMLElement, targetPosition?: 'before' | 'after' | 'inside' | 'replace'): ElementSelection {
    console.log('🎯 [ElementSelector] Element selected:', element);

    const selector = this.generateSelector(element);
    const fragmentHtml = element.outerHTML;
    const elementContext = this.extractElementContext(element);

    const selection: ElementSelection = {
      element,
      selector,
      fragmentHtml,
      elementContext,
      targetPosition: targetPosition || 'replace'
    };

    this.editModeState.selectedElement = selection;
    this.highlightSelectedElement(element);
    this.onSelectionChange?.(selection);

    return selection;
  }

  /**
   * Clear current selection
   */
  clearSelection(): void {
    console.log('🎯 [ElementSelector] Clearing selection');
    this.editModeState.selectedElement = null;
    this.clearHighlights();
    this.onSelectionChange?.(null);
  }

  /**
   * Get current selection
   */
  getSelection(): ElementSelection | null {
    return this.editModeState.selectedElement;
  }

  /**
   * Check if edit mode is active
   */
  isEditModeActive(): boolean {
    return this.editModeState.isActive;
  }

  /**
   * Generate CSS selector for element
   * Prioritizes ID selectors for reliable targeting
   */
  private generateSelector(element: HTMLElement): string {
    console.log('🎯 [ElementSelector] Generating selector for element:', element.tagName, element.id, element.className);

    // PRIORITY 1: Try ID first (most reliable)
    if (element.id) {
      console.log('🎯 [ElementSelector] Using ID selector:', `#${element.id}`);
      return `#${element.id}`;
    }

    // PRIORITY 2: Generate a unique ID if element doesn't have one
    // This ensures future edits can target this element reliably
    const generatedId = this.generateUniqueId(element);
    element.id = generatedId;
    console.log('🎯 [ElementSelector] Generated new ID for element:', generatedId);
    return `#${generatedId}`;
  }

  /**
   * Generate a unique ID for an element
   */
  private generateUniqueId(element: HTMLElement): string {
    const tagName = element.tagName.toLowerCase();
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 6);

    // Try to create a semantic ID based on element content or purpose
    let semanticPart = '';

    // Check for common button text patterns
    const textContent = element.textContent?.trim().toLowerCase() || '';
    if (textContent) {
      const commonWords = ['add', 'edit', 'delete', 'save', 'cancel', 'submit', 'login', 'signup', 'contact', 'view', 'details'];
      const foundWord = commonWords.find(word => textContent.includes(word));
      if (foundWord) {
        semanticPart = foundWord + '-';
      }
    }

    // Check for data attributes that might indicate purpose
    const dataComponent = element.getAttribute('data-component');
    if (dataComponent) {
      semanticPart = dataComponent + '-';
    }

    return `${semanticPart}${tagName}-${timestamp}-${random}`;
  }

  /**
   * Extract context information about the element
   */
  private extractElementContext(element: HTMLElement): any {
    return {
      tagName: element.tagName.toLowerCase(),
      id: element.id || null,
      className: element.className || null,
      textContent: element.textContent?.substring(0, 100) || null,
      attributes: Array.from(element.attributes).reduce((acc, attr) => {
        acc[attr.name] = attr.value;
        return acc;
      }, {} as Record<string, string>),
      position: {
        top: element.offsetTop,
        left: element.offsetLeft,
        width: element.offsetWidth,
        height: element.offsetHeight
      }
    };
  }

  /**
   * Setup message listener for iframe communication
   */
  private setupMessageListener(): void {
    this.messageListener = (event: MessageEvent) => {
      if (event.data?.type === 'ELEMENT_CLICKED') {
        const element = event.data.element;
        console.log('🎯 [ElementSelector] Element clicked in iframe:', element);

        // Convert iframe element data to our format
        const selection: ElementSelection = {
          element: element as HTMLElement, // Note: This is element data, not actual DOM element
          selector: this.generateSelectorFromData(element),
          fragmentHtml: element.outerHTML,
          elementContext: {
            tagName: element.tagName.toLowerCase(),
            id: element.id || null,
            className: element.className || null,
            textContent: element.textContent || null,
            attributes: {},
            position: { top: 0, left: 0, width: 0, height: 0 }
          },
          targetPosition: 'replace'
        };

        this.editModeState.selectedElement = selection;
        this.onSelectionChange?.(selection);
      }
    };

    window.addEventListener('message', this.messageListener);
  }

  /**
   * Generate selector from iframe element data
   */
  private generateSelectorFromData(elementData: any): string {
    if (elementData.id) {
      return `#${elementData.id}`;
    }

    if (elementData.className) {
      const classes = elementData.className.split(' ').filter((c: string) => c.trim());
      if (classes.length > 0) {
        return `.${classes.join('.')}`;
      }
    }

    return elementData.tagName.toLowerCase();
  }

  /**
   * Add hover highlighting to all elements
   */
  private addElementHighlighting(): void {
    if (this.mode === 'iframe') {
      console.log('🎯 [ElementSelector] Adding element highlighting via iframe');
      this.sendMessageToIframe({
        type: 'toggleSelectionMode',
        active: true
      });
    } else {
      console.log('🎯 [ElementSelector] Adding element highlighting via direct DOM');
      this.addDirectDOMHighlighting();
    }
  }

  /**
   * Remove hover highlighting
   */
  private removeElementHighlighting(): void {
    if (this.mode === 'iframe') {
      console.log('🎯 [ElementSelector] Removing element highlighting via iframe');
      this.sendMessageToIframe({
        type: 'toggleSelectionMode',
        active: false
      });
    } else {
      console.log('🎯 [ElementSelector] Removing element highlighting via direct DOM');
      this.removeDirectDOMHighlighting();
    }
  }

  /**
   * Add direct DOM highlighting for SPAShell mode
   */
  private addDirectDOMHighlighting(): void {
    if (!this.containerRef?.current) {
      console.warn('🎯 [ElementSelector] Container ref not available for direct DOM highlighting');
      return;
    }

    const container = this.containerRef.current;

    // Add CSS for highlighting if not already present
    if (!document.getElementById('element-selector-styles')) {
      const style = document.createElement('style');
      style.id = 'element-selector-styles';
      style.textContent = `
        .element-selector-highlight {
          outline: 2px solid #3b82f6 !important;
          outline-offset: 2px !important;
          cursor: pointer !important;
        }
        .element-selector-highlight:hover {
          outline-color: #1d4ed8 !important;
          background-color: rgba(59, 130, 246, 0.1) !important;
        }
      `;
      document.head.appendChild(style);
    }

    // Add event listeners for hover and click
    this.addDirectDOMEventListeners(container);
  }

  /**
   * Remove direct DOM highlighting
   */
  private removeDirectDOMHighlighting(): void {
    if (!this.containerRef?.current) return;

    const container = this.containerRef.current;

    // Remove all highlight classes
    const highlightedElements = container.querySelectorAll('.element-selector-highlight');
    highlightedElements.forEach(el => {
      el.classList.remove('element-selector-highlight');
    });

    // Remove event listeners
    this.removeDirectDOMEventListeners(container);
  }

  /**
   * Add event listeners for direct DOM interaction
   */
  private addDirectDOMEventListeners(container: HTMLElement): void {
    // Mouse over handler
    const handleMouseOver = (e: Event) => {
      const target = e.target as HTMLElement;
      if (target && target !== container) {
        target.classList.add('element-selector-highlight');
      }
    };

    // Mouse out handler
    const handleMouseOut = (e: Event) => {
      const target = e.target as HTMLElement;
      if (target) {
        target.classList.remove('element-selector-highlight');
      }
    };

    // Click handler
    const handleClick = (e: Event) => {
      e.preventDefault();
      e.stopPropagation();

      const target = e.target as HTMLElement;
      if (target && target !== container) {
        this.handleDirectDOMElementClick(target);
      }
    };

    // Store event listeners for cleanup
    (container as any)._elementSelectorListeners = {
      mouseover: handleMouseOver,
      mouseout: handleMouseOut,
      click: handleClick
    };

    container.addEventListener('mouseover', handleMouseOver);
    container.addEventListener('mouseout', handleMouseOut);
    container.addEventListener('click', handleClick);
  }

  /**
   * Remove event listeners for direct DOM interaction
   */
  private removeDirectDOMEventListeners(container: HTMLElement): void {
    const listeners = (container as any)._elementSelectorListeners;
    if (listeners) {
      container.removeEventListener('mouseover', listeners.mouseover);
      container.removeEventListener('mouseout', listeners.mouseout);
      container.removeEventListener('click', listeners.click);
      delete (container as any)._elementSelectorListeners;
    }
  }

  /**
   * Handle direct DOM element click
   */
  private handleDirectDOMElementClick(element: HTMLElement): void {
    console.log('🎯 [ElementSelector] Direct DOM element clicked:', element);

    // Convert DOM element to our selection format
    const selection: ElementSelection = {
      element: element,
      selector: this.generateSelector(element),
      fragmentHtml: element.outerHTML,
      elementContext: this.extractElementContext(element),
      targetPosition: 'replace'
    };

    this.editModeState.selectedElement = selection;
    this.onSelectionChange?.(selection);
  }

  /**
   * Send message to iframe
   */
  private sendMessageToIframe(message: any): void {
    if (this.iframeRef?.current?.contentWindow) {
      this.iframeRef.current.contentWindow.postMessage(message, '*');
    } else {
      console.warn('🎯 [ElementSelector] Cannot send message - iframe not available');
    }
  }

  /**
   * Cleanup method to remove event listeners
   */
  public cleanup(): void {
    if (this.messageListener) {
      window.removeEventListener('message', this.messageListener);
      this.messageListener = undefined;
    }
  }

  /**
   * Highlight the selected element
   */
  private highlightSelectedElement(element: HTMLElement): void {
    this.clearHighlights();
    element.style.outline = '2px solid #3b82f6';
    element.style.outlineOffset = '2px';
    this.editModeState.highlightedElements.push(element);
  }

  /**
   * Clear all highlights
   */
  private clearHighlights(): void {
    this.editModeState.highlightedElements.forEach(element => {
      element.style.outline = '';
      element.style.outlineOffset = '';
    });
    this.editModeState.highlightedElements = [];
  }
}

/**
 * Create element selector manager instance
 */
export function createElementSelectorManager(
  onSelectionChange?: (selection: ElementSelection | null) => void,
  onEditModeChange?: (isActive: boolean) => void,
  iframeRef?: React.RefObject<HTMLIFrameElement>,
  containerRef?: React.RefObject<HTMLElement>,
  mode: 'iframe' | 'direct' = 'iframe'
): ElementSelectorManager {
  return new ElementSelectorManager(onSelectionChange, onEditModeChange, iframeRef, containerRef, mode);
}

/**
 * Utility function to extract edit parameters from selection
 */
export function extractEditParameters(selection: ElementSelection | null) {
  if (!selection) {
    return {
      elementSelector: undefined,
      fragmentHtml: undefined,
      elementContext: undefined,
      targetPosition: undefined
    };
  }

  return {
    elementSelector: selection.selector,
    fragmentHtml: selection.fragmentHtml,
    elementContext: selection.elementContext,
    targetPosition: selection.targetPosition
  };
}
