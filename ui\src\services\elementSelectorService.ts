/**
 * Element Selector Service
 * Handles element selection, targeting, and edit mode management
 * Keeps the main editor page clean and modular
 */

export interface ElementSelection {
  element: HTMLElement;
  selector: string;
  fragmentHtml: string;
  elementContext: any;
  targetPosition?: 'before' | 'after' | 'inside' | 'replace';
}

export interface EditModeState {
  isActive: boolean;
  selectedElement: ElementSelection | null;
  highlightedElements: HTMLElement[];
}

/**
 * Element Selector Manager
 * Manages element selection and edit mode state
 */
export class ElementSelectorManager {
  private editModeState: EditModeState = {
    isActive: false,
    selectedElement: null,
    highlightedElements: []
  };

  private onSelectionChange?: (selection: ElementSelection | null) => void;
  private onEditModeChange?: (isActive: boolean) => void;

  constructor(
    onSelectionChange?: (selection: ElementSelection | null) => void,
    onEditModeChange?: (isActive: boolean) => void
  ) {
    this.onSelectionChange = onSelectionChange;
    this.onEditModeChange = onEditModeChange;
  }

  /**
   * Enter edit mode - enables element selection
   */
  enterEditMode(): void {
    console.log('🎯 [ElementSelector] Entering edit mode');
    this.editModeState.isActive = true;
    this.addElementHighlighting();
    this.onEditModeChange?.(true);
  }

  /**
   * Exit edit mode - disables element selection
   */
  exitEditMode(): void {
    console.log('🎯 [ElementSelector] Exiting edit mode');
    this.editModeState.isActive = false;
    this.clearSelection();
    this.removeElementHighlighting();
    this.onEditModeChange?.(false);
  }

  /**
   * Toggle edit mode
   */
  toggleEditMode(): void {
    if (this.editModeState.isActive) {
      this.exitEditMode();
    } else {
      this.enterEditMode();
    }
  }

  /**
   * Select an element for editing
   */
  selectElement(element: HTMLElement, targetPosition?: 'before' | 'after' | 'inside' | 'replace'): ElementSelection {
    console.log('🎯 [ElementSelector] Element selected:', element);

    const selector = this.generateSelector(element);
    const fragmentHtml = element.outerHTML;
    const elementContext = this.extractElementContext(element);

    const selection: ElementSelection = {
      element,
      selector,
      fragmentHtml,
      elementContext,
      targetPosition: targetPosition || 'replace'
    };

    this.editModeState.selectedElement = selection;
    this.highlightSelectedElement(element);
    this.onSelectionChange?.(selection);

    return selection;
  }

  /**
   * Clear current selection
   */
  clearSelection(): void {
    console.log('🎯 [ElementSelector] Clearing selection');
    this.editModeState.selectedElement = null;
    this.clearHighlights();
    this.onSelectionChange?.(null);
  }

  /**
   * Get current selection
   */
  getSelection(): ElementSelection | null {
    return this.editModeState.selectedElement;
  }

  /**
   * Check if edit mode is active
   */
  isEditModeActive(): boolean {
    return this.editModeState.isActive;
  }

  /**
   * Generate CSS selector for element
   */
  private generateSelector(element: HTMLElement): string {
    // Try ID first
    if (element.id) {
      return `#${element.id}`;
    }

    // Try class names
    if (element.className) {
      const classes = element.className.split(' ').filter(c => c.trim());
      if (classes.length > 0) {
        return `.${classes.join('.')}`;
      }
    }

    // Try data attributes
    const dataAttrs = Array.from(element.attributes)
      .filter(attr => attr.name.startsWith('data-'))
      .map(attr => `[${attr.name}="${attr.value}"]`);
    
    if (dataAttrs.length > 0) {
      return element.tagName.toLowerCase() + dataAttrs[0];
    }

    // Fallback to tag name with nth-child
    const parent = element.parentElement;
    if (parent) {
      const siblings = Array.from(parent.children);
      const index = siblings.indexOf(element) + 1;
      return `${element.tagName.toLowerCase()}:nth-child(${index})`;
    }

    return element.tagName.toLowerCase();
  }

  /**
   * Extract context information about the element
   */
  private extractElementContext(element: HTMLElement): any {
    return {
      tagName: element.tagName.toLowerCase(),
      id: element.id || null,
      className: element.className || null,
      textContent: element.textContent?.substring(0, 100) || null,
      attributes: Array.from(element.attributes).reduce((acc, attr) => {
        acc[attr.name] = attr.value;
        return acc;
      }, {} as Record<string, string>),
      position: {
        top: element.offsetTop,
        left: element.offsetLeft,
        width: element.offsetWidth,
        height: element.offsetHeight
      }
    };
  }

  /**
   * Add hover highlighting to all elements
   */
  private addElementHighlighting(): void {
    // This would be implemented with iframe communication
    console.log('🎯 [ElementSelector] Adding element highlighting');
  }

  /**
   * Remove hover highlighting
   */
  private removeElementHighlighting(): void {
    console.log('🎯 [ElementSelector] Removing element highlighting');
  }

  /**
   * Highlight the selected element
   */
  private highlightSelectedElement(element: HTMLElement): void {
    this.clearHighlights();
    element.style.outline = '2px solid #3b82f6';
    element.style.outlineOffset = '2px';
    this.editModeState.highlightedElements.push(element);
  }

  /**
   * Clear all highlights
   */
  private clearHighlights(): void {
    this.editModeState.highlightedElements.forEach(element => {
      element.style.outline = '';
      element.style.outlineOffset = '';
    });
    this.editModeState.highlightedElements = [];
  }
}

/**
 * Create element selector manager instance
 */
export function createElementSelectorManager(
  onSelectionChange?: (selection: ElementSelection | null) => void,
  onEditModeChange?: (isActive: boolean) => void
): ElementSelectorManager {
  return new ElementSelectorManager(onSelectionChange, onEditModeChange);
}

/**
 * Utility function to extract edit parameters from selection
 */
export function extractEditParameters(selection: ElementSelection | null) {
  if (!selection) {
    return {
      elementSelector: undefined,
      fragmentHtml: undefined,
      elementContext: undefined,
      targetPosition: undefined
    };
  }

  return {
    elementSelector: selection.selector,
    fragmentHtml: selection.fragmentHtml,
    elementContext: selection.elementContext,
    targetPosition: selection.targetPosition
  };
}
