// Test the multi-class selector fix
const llmServiceV3 = require('../services/llmServiceV3');

// Create a test HTML with the problematic structure
const testHTML = `
<div id="app">
  <nav class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <h1 class="text-xl font-semibold text-gray-900 mr-8">SalesPro CRM</h1>
          </div>
        </div>
      </div>
    </div>
  </nav>
  
  <main class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <div class="px-4 py-6 sm:px-0">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        
        <!-- This is the card that should be selected -->
        <div class="bg-white rounded-lg shadow p-6 element-selector-highlight">
          <div class="flex items-center">
            <div class="flex-shrink-0 bg-green-500 rounded-md p-3">
              <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <p class="text-sm font-medium text-gray-500">Closed Won</p>
              <div class="flex items-baseline">
                <p class="text-2xl font-semibold text-gray-900">$128K</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Other cards without the element-selector-highlight class -->
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0 bg-blue-500 rounded-md p-3">
              <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <p class="text-sm font-medium text-gray-500">Revenue</p>
              <div class="flex items-baseline">
                <p class="text-2xl font-semibold text-gray-900">$45K</p>
              </div>
            </div>
          </div>
        </div>
        
      </div>
    </div>
  </main>
</div>
`;

async function testMultiClassSelector() {
  console.log('🧪 Testing Multi-Class Selector Fix\n');

  const service = llmServiceV3;
  
  // Test the problematic selector from the curl request
  const selector = '.bg-white.rounded-lg.shadow.p-6.element-selector-highlight';
  
  console.log(`🔍 Testing selector: ${selector}`);
  console.log('📄 HTML length:', testHTML.length);
  
  const fragment = service.extractFragment(testHTML, selector);
  
  if (fragment) {
    console.log('✅ SUCCESS: Fragment extracted!');
    console.log('📏 Fragment length:', fragment.length);
    console.log('📄 Fragment preview:');
    console.log(fragment.substring(0, 200) + '...');
    
    // Verify it contains the expected content
    if (fragment.includes('Closed Won') && fragment.includes('$128K')) {
      console.log('✅ CONTENT VERIFICATION: Fragment contains expected content');
    } else {
      console.log('❌ CONTENT VERIFICATION: Fragment missing expected content');
    }
  } else {
    console.log('❌ FAILED: No fragment extracted');
  }
  
  console.log('\n' + '='.repeat(60));
  
  // Test other selectors for comparison
  const testSelectors = [
    '.bg-white',
    '.element-selector-highlight',
    '.bg-white.rounded-lg',
    '.shadow.p-6'
  ];
  
  console.log('🔍 Testing other selectors for comparison:\n');
  
  for (const testSelector of testSelectors) {
    const testFragment = service.extractFragment(testHTML, testSelector);
    console.log(`Selector: ${testSelector}`);
    console.log(`Result: ${testFragment ? 'SUCCESS' : 'FAILED'}`);
    if (testFragment) {
      console.log(`Length: ${testFragment.length}`);
    }
    console.log('');
  }
}

// Run the test
testMultiClassSelector().catch(console.error);
