/**
 * HTML Merge Service
 * Handles reliable fragment merging using Cheerio-based manipulation
 */

const cheerio = require('cheerio');

class HtmlMergeService {
  
  /**
   * Merge edited fragment back into original HTML using ID-based replacement
   * @param {string} originalHtml - Complete HTML document
   * @param {string} fragmentHtml - Original fragment HTML
   * @param {string} editedFragment - Edited fragment HTML
   * @returns {Object} - { success: boolean, html: string, error?: string }
   */
  mergeFragment(originalHtml, fragmentHtml, editedFragment) {
    console.log('🔄 [HtmlMerge] Starting fragment merge using DOM-based approach');
    console.log('📄 [HtmlMerge] Original HTML length:', originalHtml?.length || 0);
    console.log('📄 [HtmlMerge] Fragment length:', fragmentHtml?.length || 0);
    console.log('📄 [HtmlMerge] Edited fragment length:', editedFragment?.length || 0);

    try {
      // Step 1: Clean the original HTML from any markdown wrapping
      const cleanOriginalHtml = this.cleanMarkdownWrapper(originalHtml);
      
      // Step 2: Extract element ID from fragment
      const elementId = this.extractElementId(fragmentHtml);
      if (!elementId) {
        console.log('⚠️ [HtmlMerge] No ID found, falling back to string replacement');
        return this.fallbackStringReplacement(cleanOriginalHtml, fragmentHtml, editedFragment);
      }

      console.log('🎯 [HtmlMerge] Found element ID:', elementId);

      // Step 3: Use DOM-based replacement
      const mergedHtml = this.replaceElementById(cleanOriginalHtml, elementId, editedFragment);
      
      if (mergedHtml && mergedHtml !== cleanOriginalHtml) {
        console.log('📄 [HtmlMerge] Final merged HTML length:', mergedHtml.length);
        console.log('✅ [HtmlMerge] Fragment successfully merged using DOM-based replacement');
        return { success: true, html: mergedHtml };
      } else {
        console.log('⚠️ [HtmlMerge] DOM replacement failed, trying fallback');
        return this.fallbackStringReplacement(cleanOriginalHtml, fragmentHtml, editedFragment);
      }

    } catch (error) {
      console.error('❌ [HtmlMerge] Error during fragment merge:', error);
      return { 
        success: false, 
        html: originalHtml, 
        error: error.message 
      };
    }
  }

  /**
   * Clean markdown wrapper from HTML content
   * @param {string} html - HTML content that might be wrapped in markdown
   * @returns {string} - Cleaned HTML
   */
  cleanMarkdownWrapper(html) {
    if (!html || typeof html !== 'string') {
      return '';
    }

    let cleaned = html;
    if (cleaned.includes('```html')) {
      cleaned = cleaned.replace(/.*```html\s*/s, '').replace(/\s*```.*$/s, '');
      console.log('🧹 [HtmlMerge] Cleaned markdown wrapper from HTML');
    }

    return cleaned.trim();
  }

  /**
   * Extract element ID from fragment HTML
   * @param {string} fragmentHtml - Fragment HTML content
   * @returns {string|null} - Element ID or null if not found
   */
  extractElementId(fragmentHtml) {
    if (!fragmentHtml || typeof fragmentHtml !== 'string') {
      return null;
    }

    const idMatch = fragmentHtml.match(/id\s*=\s*["']([^"']+)["']/);
    if (idMatch) {
      console.log('🎯 [HtmlMerge] Found element ID in fragment:', idMatch[1]);
      return idMatch[1];
    }

    console.log('⚠️ [HtmlMerge] No ID found in fragment HTML');
    return null;
  }



  /**
   * Replace element by ID using Cheerio manipulation
   * @param {string} html - Complete HTML document
   * @param {string} elementId - ID of element to replace
   * @param {string} newElementHtml - New element HTML
   * @returns {string|null} - Updated HTML or null if failed
   */
  replaceElementById(html, elementId, newElementHtml) {
    try {
      console.log('🔧 [HtmlMerge] Using Cheerio-based replacement for element:', elementId);

      // Load HTML with Cheerio
      const $ = cheerio.load(html, {
        withDomLvl1: true,
        normalizeWhitespace: false,
        xmlMode: false,
        decodeEntities: false
      });

      // Find element by ID (escape special characters in ID)
      const escapedId = elementId.replace(/[!"#$%&'()*+,.\/:;<=>?@[\\\]^`{|}~]/g, '\\$&');
      const element = $(`#${escapedId}`);
      if (element.length === 0) {
        console.log('❌ [HtmlMerge] Element not found with ID:', elementId);
        return null;
      }

      console.log('🔍 [HtmlMerge] Found element:', element.prop('tagName'));
      console.log('🔍 [HtmlMerge] Element classes:', element.attr('class'));

      // Validate new element HTML
      if (!newElementHtml || newElementHtml.trim().length === 0) {
        console.log('❌ [HtmlMerge] New element HTML is empty');
        return null;
      }

      // Replace the element with new HTML
      element.replaceWith(newElementHtml);

      // Return the updated HTML
      const updatedHtml = $.html();
      console.log('✅ [HtmlMerge] Cheerio-based replacement successful');

      return updatedHtml;

    } catch (error) {
      console.error('❌ [HtmlMerge] Cheerio replacement error:', error);
      return null;
    }
  }

  /**
   * Fallback string replacement method with enhanced strategies
   * @param {string} originalHtml - Original HTML
   * @param {string} fragmentHtml - Original fragment
   * @param {string} editedFragment - Edited fragment
   * @returns {Object} - { success: boolean, html: string }
   */
  fallbackStringReplacement(originalHtml, fragmentHtml, editedFragment) {
    console.log('🔄 [HtmlMerge] Using fallback string replacement');

    try {
      // Strategy 1: Exact string match
      if (originalHtml.includes(fragmentHtml)) {
        const mergedHtml = originalHtml.replace(fragmentHtml, editedFragment);
        console.log('✅ [HtmlMerge] Fallback: Exact string replacement successful');
        return { success: true, html: mergedHtml };
      }

      // Strategy 2: Clean match (remove UI classes)
      const cleanOriginalFragment = fragmentHtml.replace(/\s*element-selector-highlight/g, '');
      const cleanEditedFragment = editedFragment.replace(/\s*element-selector-highlight/g, '');

      if (originalHtml.includes(cleanOriginalFragment)) {
        const mergedHtml = originalHtml.replace(cleanOriginalFragment, cleanEditedFragment);
        console.log('✅ [HtmlMerge] Fallback: Cleaned string replacement successful');
        return { success: true, html: mergedHtml };
      }

      // Strategy 3: Class-based replacement using Cheerio
      const classMatch = fragmentHtml.match(/class\s*=\s*["']([^"']+)["']/);
      if (classMatch) {
        const classes = classMatch[1].split(/\s+/).filter(c => c && !c.includes('element-selector-highlight'));
        if (classes.length > 0) {
          console.log('🎯 [HtmlMerge] Trying class-based replacement with classes:', classes);

          try {
            const $ = cheerio.load(originalHtml, {
              withDomLvl1: true,
              normalizeWhitespace: false,
              xmlMode: false,
              decodeEntities: false
            });

            // Try to find element by first class
            const selector = `.${classes[0]}`;
            const elements = $(selector);

            if (elements.length === 1) {
              // Only replace if we find exactly one match to avoid ambiguity
              console.log('🔍 [HtmlMerge] Found unique element with class:', classes[0]);
              elements.replaceWith(cleanEditedFragment);
              const updatedHtml = $.html();
              console.log('✅ [HtmlMerge] Class-based replacement successful');
              return { success: true, html: updatedHtml };
            } else if (elements.length > 1) {
              console.log('⚠️ [HtmlMerge] Multiple elements found with class, skipping to avoid ambiguity');
            } else {
              console.log('❌ [HtmlMerge] No elements found with class:', classes[0]);
            }
          } catch (cheerioError) {
            console.error('❌ [HtmlMerge] Cheerio class-based replacement error:', cheerioError);
          }
        }
      }

      // Strategy 4: Text content-based replacement
      const textMatch = fragmentHtml.match(/>([^<]+)</);
      if (textMatch && textMatch[1].trim()) {
        const textContent = textMatch[1].trim();
        console.log('🎯 [HtmlMerge] Trying text-based replacement for:', textContent);

        // Create a regex to find the element with this text content
        const tagMatch = fragmentHtml.match(/^<(\w+)/);
        if (tagMatch) {
          const tagName = tagMatch[1];
          const textRegex = new RegExp(`<${tagName}[^>]*>${textContent.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}</${tagName}>`, 'g');

          if (textRegex.test(originalHtml)) {
            const mergedHtml = originalHtml.replace(textRegex, cleanEditedFragment);
            console.log('✅ [HtmlMerge] Text-based replacement successful');
            return { success: true, html: mergedHtml };
          }
        }
      }

      console.log('❌ [HtmlMerge] All fallback strategies failed');
      return { success: false, html: originalHtml };

    } catch (error) {
      console.error('❌ [HtmlMerge] Fallback replacement error:', error);
      return { success: false, html: originalHtml };
    }
  }

  /**
   * Validate merged HTML
   * @param {string} originalHtml - Original HTML
   * @param {string} mergedHtml - Merged HTML
   * @returns {boolean} - True if merge looks valid
   */
  validateMerge(originalHtml, mergedHtml) {
    if (!mergedHtml || typeof mergedHtml !== 'string') {
      return false;
    }

    // Basic validation: merged HTML should be similar in size
    const sizeDifference = Math.abs(mergedHtml.length - originalHtml.length);
    const maxAllowedDifference = originalHtml.length * 0.1; // 10% difference allowed

    if (sizeDifference > maxAllowedDifference) {
      console.log('⚠️ [HtmlMerge] Merged HTML size difference too large:', sizeDifference);
      return false;
    }

    // Check if it still contains the app div
    if (originalHtml.includes('<div id="app">') && !mergedHtml.includes('<div id="app">')) {
      console.log('⚠️ [HtmlMerge] Merged HTML missing app div structure');
      return false;
    }

    return true;
  }
}

module.exports = new HtmlMergeService();
