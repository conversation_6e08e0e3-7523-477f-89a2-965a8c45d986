/**
 * Test the session list API endpoint directly
 */

const fetch = require('node-fetch');

async function testSessionListAPI() {
  try {
    console.log('🧪 Testing Session List API...\n');

    const url = 'http://localhost:5000/api/page_gen/session/list';
    const payload = {
      projectId: "228",
      page: {
        pageNum: 1,
        pageSize: 30
      }
    };

    console.log('📤 Making request to:', url);
    console.log('📤 Payload:', JSON.stringify(payload, null, 2));

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': 'connect.sid=s%3AIjskIATLG0PVEItcdq5S2b5EDuF9ezAc.IC8oO7s1s%2BsvV1rSrF9CTQ6baUsMJqQe7siFAQ4xitg'
      },
      body: JSON.stringify(payload)
    });

    console.log('📥 Response status:', response.status);
    console.log('📥 Response headers:', Object.fromEntries(response.headers.entries()));

    const responseText = await response.text();
    console.log('📥 Response body:', responseText);

    if (response.ok) {
      try {
        const data = JSON.parse(responseText);
        console.log('\n✅ API Response parsed successfully:');
        console.log('- Sessions count:', data.sessions?.length || 0);
        console.log('- Total count:', data.totalCount);
        console.log('- Page info:', data.page);
        
        if (data.sessions && data.sessions.length > 0) {
          console.log('\n📄 First session:');
          console.log(JSON.stringify(data.sessions[0], null, 2));
        }
      } catch (parseError) {
        console.log('❌ Failed to parse JSON response:', parseError.message);
      }
    } else {
      console.log('❌ API request failed');
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Also test if the server is running
async function testServerHealth() {
  try {
    console.log('🏥 Testing server health...');
    const response = await fetch('http://localhost:5000/health', {
      method: 'GET'
    });
    
    if (response.ok) {
      console.log('✅ Server is running');
    } else {
      console.log('⚠️ Server responded with status:', response.status);
    }
  } catch (error) {
    console.log('❌ Server is not running or not accessible:', error.message);
  }
}

async function runTests() {
  await testServerHealth();
  console.log('\n' + '='.repeat(50) + '\n');
  await testSessionListAPI();
}

runTests();
