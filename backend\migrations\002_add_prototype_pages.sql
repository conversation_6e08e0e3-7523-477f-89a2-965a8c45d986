-- Migration: Add prototype_pages table for permanent page storage
-- This replaces the temporary session-based page storage

-- =====================================================
-- 1. PROTOTYPE PAGES TABLE (PERMANENT)
-- =====================================================
-- Stores pages permanently linked to prototypes (not sessions)
CREATE TABLE IF NOT EXISTS prototype_pages (
    id SERIAL PRIMARY KEY,
    prototype_id INTEGER NOT NULL REFERENCES prototypes(id) ON DELETE CASCADE,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(500) NOT NULL,
    html_content TEXT NOT NULL,
    css_content TEXT,
    page_order INTEGER DEFAULT 1,
    is_default BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_prototype_pages_prototype_id ON prototype_pages(prototype_id);
CREATE INDEX IF NOT EXISTS idx_prototype_pages_user_id ON prototype_pages(user_id);
CREATE INDEX IF NOT EXISTS idx_prototype_pages_created_at ON prototype_pages(created_at);

-- Add unique constraint to ensure only one default page per prototype
CREATE UNIQUE INDEX IF NOT EXISTS idx_prototype_pages_default_unique 
ON prototype_pages(prototype_id) WHERE is_default = TRUE;

-- =====================================================
-- 2. UPDATE TRIGGER FOR UPDATED_AT
-- =====================================================
CREATE OR REPLACE FUNCTION update_prototype_pages_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_prototype_pages_updated_at
    BEFORE UPDATE ON prototype_pages
    FOR EACH ROW
    EXECUTE FUNCTION update_prototype_pages_updated_at();

-- =====================================================
-- 3. MIGRATE EXISTING SESSION DATA (IF ANY)
-- =====================================================
-- Migrate active sessions to permanent pages
INSERT INTO prototype_pages (prototype_id, user_id, title, html_content, page_order, is_default, created_at)
SELECT 
    prototype_id,
    user_id,
    COALESCE(page_url, 'Untitled Page') as title,
    page_html as html_content,
    1 as page_order,
    TRUE as is_default,
    created_at
FROM prototype_sessions 
WHERE session_state = 'active' 
AND expires_at > CURRENT_TIMESTAMP
AND NOT EXISTS (
    SELECT 1 FROM prototype_pages pp 
    WHERE pp.prototype_id = prototype_sessions.prototype_id
)
ON CONFLICT DO NOTHING;

-- =====================================================
-- 4. COMMENTS FOR DOCUMENTATION
-- =====================================================
COMMENT ON TABLE prototype_pages IS 'Permanent storage for prototype pages - replaces temporary session-based storage';
COMMENT ON COLUMN prototype_pages.title IS 'Page title displayed in the UI';
COMMENT ON COLUMN prototype_pages.html_content IS 'Complete HTML content for the page';
COMMENT ON COLUMN prototype_pages.css_content IS 'Optional CSS content for the page';
COMMENT ON COLUMN prototype_pages.page_order IS 'Order of pages within a prototype';
COMMENT ON COLUMN prototype_pages.is_default IS 'Whether this is the default/main page for the prototype';
