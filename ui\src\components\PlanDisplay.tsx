import React from 'react';

interface PlanDisplayProps {
  planData: any;
  className?: string;
}

/**
 * Reusable component for displaying structured plan data
 * Used in both PlanReviewPageV3 and EditorPageV3Refactored
 */
export const PlanDisplay: React.FC<PlanDisplayProps> = ({ planData, className = "" }) => {
  if (!planData) return null;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Project Overview */}
      {planData.overview && (
        <div className="bg-blue-50 rounded-lg p-6 border border-blue-100">
          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center flex-shrink-0">
              <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-bold text-gray-900 mb-2">Project Overview</h3>
              <p className="text-gray-700 text-sm leading-relaxed">
                {planData.overview}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Implementation Sections */}
      {planData.sections && planData.sections.length > 0 && (
        <div>
          <h3 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
            <span className="w-6 h-6 bg-purple-600 rounded-md flex items-center justify-center mr-2">
              <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </span>
            Implementation Sections
          </h3>
          <div className="space-y-4">
            {planData.sections.map((section: any, index: number) => (
              <div key={index} className="bg-white rounded-lg border border-gray-200 p-4">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-md flex items-center justify-center text-sm font-bold">
                    {index + 1}
                  </div>
                  <div className="flex-1">
                    <h4 className="text-base font-semibold text-gray-900 mb-1">{section.title}</h4>
                    {section.description && <p className="text-gray-600 mb-3 text-sm">{section.description}</p>}
                    {section.details && section.details.length > 0 && (
                      <div className="space-y-1">
                        {section.details.map((detail: string, detailIndex: number) => (
                          <div key={detailIndex} className="flex items-start space-x-2">
                            <div className="w-1 h-1 bg-green-500 rounded-full flex-shrink-0 mt-2"></div>
                            <span className="text-gray-700 text-sm leading-relaxed">{detail}</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Dynamic Additional Sections */}
      {Object.keys(planData).filter(key => !['overview', 'sections'].includes(key) && Array.isArray(planData[key])).map(key => {
        const sectionData = planData[key];
        if (!sectionData || sectionData.length === 0) return null;

        return (
          <div key={key} className="bg-gray-50 rounded-lg p-6 border border-gray-200">
            <div className="flex items-start space-x-3">
              <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center flex-shrink-0">
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div className="flex-1">
                <h3 className="text-lg font-bold text-gray-900 mb-3 capitalize">{key.replace(/([A-Z])/g, ' $1').trim()}</h3>
                <div className="space-y-2">
                  {sectionData.map((item: string, itemIndex: number) => (
                    <div key={itemIndex} className="flex items-start space-x-2">
                      <div className="w-1 h-1 bg-green-500 rounded-full flex-shrink-0 mt-2"></div>
                      <span className="text-gray-700 text-sm leading-relaxed">{item}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );
      })}
    </div>
  );
};
