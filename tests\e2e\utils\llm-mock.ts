import { Page, Route, Request } from '@playwright/test';
import { StreamingMockGenerator, MockStreamingResponse } from './streaming-helpers';

/**
 * LLM API Mocking Utilities
 * 
 * Provides comprehensive mocking for LLM API calls including:
 * - Request/response interception
 * - Streaming response simulation
 * - Error scenario testing
 * - Performance simulation
 */

export interface LLMMockConfig {
  enableMocking: boolean;
  responseDelay: number;
  errorRate: number;
  timeoutRate: number;
  customResponses: Map<string, any>;
}

export class LLMMockService {
  private page: Page;
  private config: LLMMockConfig;
  private interceptedRequests: Request[] = [];
  
  constructor(page: Page, config: Partial<LLMMockConfig> = {}) {
    this.page = page;
    this.config = {
      enableMocking: true,
      responseDelay: 1000,
      errorRate: 0,
      timeoutRate: 0,
      customResponses: new Map(),
      ...config
    };
  }
  
  /**
   * Setup LLM API mocking
   */
  async setupMocking() {
    if (!this.config.enableMocking) {
      console.log('🔧 LLM mocking disabled');
      return;
    }
    
    console.log('🎭 Setting up LLM API mocking...');
    
    // Intercept LLM API calls
    await this.page.route('**/api/llm/v3/**', (route) => this.handleLLMRequest(route));
    await this.page.route('**/litellm-production-744f.up.railway.app/**', (route) => this.handleLiteLLMRequest(route));
    
    console.log('✅ LLM API mocking setup complete');
  }
  
  /**
   * Handle LLM API requests
   */
  private async handleLLMRequest(route: Route) {
    const request = route.request();
    this.interceptedRequests.push(request);
    
    const url = request.url();
    const method = request.method();
    const postData = request.postData();
    
    console.log(`🔍 Intercepted LLM request: ${method} ${url}`);
    
    try {
      // Parse request data
      const requestData = postData ? JSON.parse(postData) : {};
      const prompt = requestData.prompt || requestData.message || '';
      
      // Determine response type based on endpoint
      if (url.includes('/generate-html')) {
        await this.handleGenerateHTML(route, prompt);
      } else if (url.includes('/edit')) {
        await this.handleEditHTML(route, prompt, requestData.html);
      } else if (url.includes('/plan')) {
        await this.handleGeneratePlan(route, prompt);
      } else {
        // Default response
        await this.handleDefaultResponse(route, requestData);
      }
      
    } catch (error) {
      console.error('❌ Error handling LLM request:', error);
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Mock service error' })
      });
    }
  }
  
  /**
   * Handle LiteLLM proxy requests
   */
  private async handleLiteLLMRequest(route: Route) {
    const request = route.request();
    console.log(`🔍 Intercepted LiteLLM request: ${request.url()}`);
    
    // For LiteLLM, we'll simulate the actual API response format
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        choices: [{
          message: {
            content: 'Mock LiteLLM response'
          }
        }]
      })
    });
  }
  
  /**
   * Handle HTML generation requests
   */
  private async handleGenerateHTML(route: Route, prompt: string) {
    console.log(`🎨 Handling generate HTML request for prompt: "${prompt}"`);
    
    // Check for custom response
    const customResponse = this.config.customResponses.get(prompt);
    if (customResponse) {
      await this.sendStreamingResponse(route, customResponse);
      return;
    }
    
    // Generate mock streaming response
    const mockResponse = StreamingMockGenerator.generatePrototypeStream(prompt);
    await this.sendStreamingResponse(route, mockResponse);
  }
  
  /**
   * Handle HTML editing requests
   */
  private async handleEditHTML(route: Route, prompt: string, originalHTML: string) {
    console.log(`✏️ Handling edit HTML request for prompt: "${prompt}"`);
    
    // Generate mock edit response
    const mockResponse = StreamingMockGenerator.generateEditStream(prompt, originalHTML || '');
    await this.sendStreamingResponse(route, mockResponse);
  }
  
  /**
   * Handle plan generation requests
   */
  private async handleGeneratePlan(route: Route, prompt: string) {
    console.log(`📋 Handling generate plan request for prompt: "${prompt}"`);
    
    const mockPlan = {
      title: 'Generated Plan',
      description: `Plan for: ${prompt}`,
      steps: [
        'Analyze requirements',
        'Design layout',
        'Implement components',
        'Add styling',
        'Test functionality'
      ],
      estimatedTime: '15-20 minutes'
    };
    
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify(mockPlan)
    });
  }
  
  /**
   * Handle default responses
   */
  private async handleDefaultResponse(route: Route, requestData: any) {
    console.log('🔄 Handling default LLM response');
    
    await route.fulfill({
      status: 200,
      contentType: 'application/json',
      body: JSON.stringify({
        success: true,
        message: 'Mock response',
        data: requestData
      })
    });
  }
  
  /**
   * Send streaming response
   */
  private async sendStreamingResponse(route: Route, mockResponse: MockStreamingResponse) {
    console.log('📡 Sending streaming response...');
    
    // Simulate error scenarios
    if (Math.random() < this.config.errorRate) {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Simulated LLM error' })
      });
      return;
    }
    
    // Simulate timeout scenarios
    if (Math.random() < this.config.timeoutRate) {
      await new Promise(resolve => setTimeout(resolve, 60000)); // Long delay
      return;
    }
    
    // Create streaming response
    const streamingBody = this.createStreamingBody(mockResponse);
    
    await route.fulfill({
      status: 200,
      contentType: 'text/event-stream',
      headers: {
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*'
      },
      body: streamingBody
    });
  }
  
  /**
   * Create streaming response body
   */
  private createStreamingBody(mockResponse: MockStreamingResponse): string {
    let body = '';
    
    for (const event of mockResponse.events) {
      if (event.event === 'start') {
        body += 'event:start\n\n';
      } else if (event.event === 'end') {
        body += 'event:end\n\n';
      } else if (event.event === 'data') {
        body += `data:${event.data}\n`;
      }
    }
    
    return body;
  }
  
  /**
   * Add custom response for specific prompt
   */
  addCustomResponse(prompt: string, response: MockStreamingResponse) {
    this.config.customResponses.set(prompt, response);
    console.log(`📝 Added custom response for prompt: "${prompt}"`);
  }
  
  /**
   * Simulate network issues
   */
  async simulateNetworkIssues() {
    console.log('🌐 Simulating network issues...');
    
    await this.page.route('**/api/llm/v3/**', async (route) => {
      // Random delays and failures
      const delay = Math.random() * 5000; // 0-5 second delay
      await new Promise(resolve => setTimeout(resolve, delay));
      
      if (Math.random() < 0.3) { // 30% failure rate
        await route.abort('failed');
      } else {
        await route.continue();
      }
    });
  }
  
  /**
   * Get intercepted requests for analysis
   */
  getInterceptedRequests(): Request[] {
    return this.interceptedRequests;
  }
  
  /**
   * Clear intercepted requests
   */
  clearInterceptedRequests() {
    this.interceptedRequests = [];
  }
  
  /**
   * Disable mocking (use real APIs)
   */
  async disableMocking() {
    console.log('🔓 Disabling LLM API mocking...');
    await this.page.unroute('**/api/llm/v3/**');
    await this.page.unroute('**/litellm-production-744f.up.railway.app/**');
    this.config.enableMocking = false;
  }
  
  /**
   * Get mocking statistics
   */
  getMockingStats() {
    return {
      totalRequests: this.interceptedRequests.length,
      config: this.config,
      customResponseCount: this.config.customResponses.size
    };
  }
}
