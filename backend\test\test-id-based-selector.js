// Test the ID-based selector approach
const llmServiceV3 = require('../services/llmServiceV3');

// Create test HTML with ID-based elements (following our new approach)
const testHTML = `
<div id="app">
  <nav id="main-nav" class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <h1 id="app-title" class="text-xl font-semibold text-gray-900 mr-8">SalesPro CRM</h1>
          </div>
        </div>
      </div>
    </div>
  </nav>
  
  <main id="main-content" class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
    <div class="px-4 py-6 sm:px-0">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        
        <!-- Card with unique ID - this should be easily selectable -->
        <div id="sales-card" class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0 bg-green-500 rounded-md p-3">
              <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <p class="text-sm font-medium text-gray-500">Closed Won</p>
              <div class="flex items-baseline">
                <p class="text-2xl font-semibold text-gray-900">$128K</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Another card with ID -->
        <div id="revenue-card" class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center">
            <div class="flex-shrink-0 bg-blue-500 rounded-md p-3">
              <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <p class="text-sm font-medium text-gray-500">Revenue</p>
              <div class="flex items-baseline">
                <p class="text-2xl font-semibold text-gray-900">$45K</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Button with ID -->
        <div id="action-panel" class="bg-white rounded-lg shadow p-6">
          <h3 class="text-lg font-semibold mb-4">Quick Actions</h3>
          <button id="add-contact-btn" class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
            Add Contact
          </button>
          <button id="generate-report-btn" class="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700 ml-2">
            Generate Report
          </button>
        </div>
        
      </div>
    </div>
  </main>
</div>
`;

async function testIdBasedSelectors() {
  console.log('🧪 Testing ID-Based Selector Approach\n');
  
  const service = llmServiceV3;
  
  // Test various ID selectors
  const testSelectors = [
    '#sales-card',
    '#revenue-card', 
    '#add-contact-btn',
    '#generate-report-btn',
    '#action-panel',
    '#main-nav',
    '#app-title'
  ];
  
  console.log('📄 HTML length:', testHTML.length);
  console.log('🎯 Testing ID-based selectors:\n');
  
  let successCount = 0;
  let totalTests = testSelectors.length;
  
  for (const selector of testSelectors) {
    console.log(`🔍 Testing selector: ${selector}`);
    
    const fragment = service.extractFragment(testHTML, selector);
    
    if (fragment) {
      successCount++;
      console.log('✅ SUCCESS: Fragment extracted!');
      console.log(`📏 Fragment length: ${fragment.length} characters`);
      
      // Show a preview of the extracted content
      const preview = fragment.substring(0, 100).replace(/\s+/g, ' ').trim();
      console.log(`📄 Preview: ${preview}...`);
      
      // Verify the fragment contains the expected ID
      const expectedId = selector.substring(1); // Remove the #
      if (fragment.includes(`id="${expectedId}"`)) {
        console.log('✅ ID VERIFICATION: Fragment contains correct ID');
      } else {
        console.log('❌ ID VERIFICATION: Fragment missing expected ID');
      }
    } else {
      console.log('❌ FAILED: No fragment extracted');
    }
    
    console.log('');
  }
  
  console.log('='.repeat(60));
  console.log(`📊 RESULTS: ${successCount}/${totalTests} tests passed`);
  console.log(`📈 Success rate: ${Math.round((successCount/totalTests) * 100)}%`);
  
  if (successCount === totalTests) {
    console.log('🎉 ALL TESTS PASSED! ID-based selectors are working perfectly.');
  } else {
    console.log('⚠️ Some tests failed. ID-based selector needs improvement.');
  }
}

// Run the test
testIdBasedSelectors().catch(console.error);
