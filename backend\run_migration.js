/**
 * Simple migration runner for prototype_pages table
 */

const { pool } = require('./services/promptDbService');
const fs = require('fs');
const path = require('path');

async function runMigration() {
  try {
    console.log('🚀 Starting migration...');

    // Check if table already exists
    const tableCheck = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'prototype_pages'
      );
    `);

    if (tableCheck.rows[0].exists) {
      console.log('✅ prototype_pages table already exists, skipping migration');
      return;
    }

    // Read migration file
    const migrationPath = path.join(__dirname, 'migrations', '002_add_prototype_pages.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    console.log('📦 Running migration...');
    await pool.query(migrationSQL);

    console.log('✅ Migration completed successfully!');

    // Test the new service
    console.log('🧪 Testing PrototypePageService...');
    const prototypePageService = require('./services/prototypePageService');

    // Test basic functionality
    const testCount = await prototypePageService.getPagesCountByPrototype(1, 1);
    console.log('✅ Service test passed, page count:', testCount);

  } catch (error) {
    console.error('❌ Migration failed:', error);
    console.error('Error details:', error.message);
    if (error.code) {
      console.error('Error code:', error.code);
    }
  } finally {
    await pool.end();
    process.exit(0);
  }
}

runMigration();
