/**
 * Production-ready hook for EditorV3 core logic
 * Extracts and modularizes the main editor functionality
 */

import { useState, useRef, useCallback, useEffect } from 'react';
import { PatchManager } from '../modules/spa/core/PatchManager';

// ============================================================================
// TYPES
// ============================================================================

export type ViewMode = 'preview' | 'code';

export interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  type?: 'plan' | 'code' | 'message';
}

export interface Page {
  id: string;
  name: string;
  content: string;
  isActive: boolean;
  lastUpdated?: Date;
}

export interface ElementInfo {
  selector: string;
  tagName: string;
  textContent: string;
  attributes: Record<string, string>;
  isNavigation: boolean;
  isInteractive: boolean;
  implementationType?: string;
  implementationReason?: string;
  outerHTML?: string;
  intentData?: {
    userIntent: string;
    suggestion?: string;
  };
}

export interface OperationProgress {
  percentage: number;
  phase: 'analyzing' | 'generating' | 'finalizing' | 'complete';
  estimatedTimeRemaining: number;
  bytesReceived: number;
  startTime: number;
}

export interface EditorState {
  // Content state
  htmlContent: string;
  streamingContent: string;
  stableIframeContent: string;

  // UI state
  viewMode: ViewMode;
  isGenerating: boolean;
  isLinking: boolean;
  operationProgress: OperationProgress | null;

  // Multi-page state
  pages: Page[];
  currentPageId: string;

  // Chat state
  messages: ChatMessage[];
  input: string;

  // Selection state
  selectedElement: ElementInfo | null;
  showImplementModal: boolean;
  customFunctionality: string;
}

export interface EditorActions {
  // Content actions
  setHtmlContent: (content: string) => void;
  setStreamingContent: (content: string) => void;
  setStableIframeContent: (content: string) => void;

  // UI actions
  setViewMode: (mode: ViewMode) => void;
  setIsGenerating: (generating: boolean) => void;
  setIsLinking: (linking: boolean) => void;

  // Multi-page actions
  addPage: (page: Omit<Page, 'lastUpdated'>) => void;
  updatePage: (pageId: string, updates: Partial<Page>) => void;
  switchToPage: (pageId: string) => void;
  reloadCurrentPage: (sessionId?: string) => Promise<boolean>;

  // Chat actions
  addMessage: (message: ChatMessage) => void;
  setInput: (input: string) => void;
  clearInput: () => void;

  // Selection actions
  setSelectedElement: (element: ElementInfo | null) => void;
  setShowImplementModal: (show: boolean) => void;
  setCustomFunctionality: (functionality: string) => void;

  // Core operations
  generateFromPrompt: (prompt: string, pageTitle?: string) => Promise<void>;
  editContent: (prompt: string, additionalMessages?: ChatMessage[], elementSelector?: string, implementationType?: string, selectedElementData?: any) => Promise<void>;
  linkAllPages: () => Promise<void>;
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Extract title from prompt text - uses clean title generation without truncation
 */
function extractTitleFromPrompt(prompt: string): string {
  try {
    // Use the same clean title generation logic as the page name service
    return generateCleanTitleFromPrompt(prompt);
  } catch (error) {
    console.error('Error extracting title from prompt:', error);
    return 'Generated Page';
  }
}

/**
 * Generate clean title from prompt using improved logic (no truncation)
 */
function generateCleanTitleFromPrompt(prompt: string): string {
  const cleanPrompt = prompt.toLowerCase().trim();

  // Common patterns for page names - exact matches first
  const patterns = [
    { keywords: ['login', 'sign in', 'signin'], name: 'Login' },
    { keywords: ['signup', 'sign up', 'register'], name: 'Sign Up' },
    { keywords: ['contact', 'contact us'], name: 'Contact' },
    { keywords: ['about', 'about us'], name: 'About' },
    { keywords: ['pricing', 'price'], name: 'Pricing' },
    { keywords: ['dashboard', 'admin'], name: 'Dashboard' },
    { keywords: ['profile', 'account'], name: 'Profile' },
    { keywords: ['settings', 'setting', 'config'], name: 'Settings' },
    { keywords: ['help', 'support', 'faq'], name: 'Help' },
    { keywords: ['blog', 'news', 'article'], name: 'Blog' },
    { keywords: ['gallery', 'portfolio'], name: 'Gallery' },
    { keywords: ['service', 'services'], name: 'Services' },
    { keywords: ['product', 'products'], name: 'Products' },
    { keywords: ['home', 'homepage'], name: 'Home' },
    { keywords: ['team', 'our team'], name: 'Team' },
    { keywords: ['career', 'careers', 'jobs'], name: 'Careers' }
  ];

  // Check for pattern matches
  for (const pattern of patterns) {
    if (pattern.keywords.some(keyword => cleanPrompt.includes(keyword))) {
      return pattern.name;
    }
  }

  // Extract meaningful words from prompt
  const words = cleanPrompt
    .replace(/^(create|make|build|add|generate|design)\s+/i, '') // Remove action words
    .replace(/\s+(page|section|component|form|modal)\s*$/i, '') // Remove common suffixes
    .split(/\s+/)
    .filter(word => word.length > 2 && !['the', 'and', 'for', 'with', 'that', 'this'].includes(word))
    .slice(0, 2); // Take first 2 meaningful words

  if (words.length > 0) {
    return words
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  }

  // Final fallback
  return 'New Page';
}

const extractHtmlFromResponse = (response: string): string => {
  if (!response) return '';

  let htmlFragment = '';

  // First, check if this is already a complete HTML document
  if (response.trim().startsWith('<!DOCTYPE html') || response.trim().startsWith('<html')) {
    return response.trim();
  }

  // Look for HTML content between ```html and ``` markers
  const htmlMatch = response.match(/```html\s*([\s\S]*?)\s*```/);
  if (htmlMatch) {
    console.log('🔧 Found HTML in markdown code block');
    htmlFragment = htmlMatch[1].trim();
  } else if (response.includes('<') && response.includes('>')) {
    console.log('🔧 Response contains HTML tags, extracting from first tag');
    // If response contains HTML tags, assume it's HTML fragment
    const firstTagIndex = response.indexOf('<');
    htmlFragment = response.substring(firstTagIndex).trim();
  } else {
    console.log('🔧 No HTML found, returning response as-is');
    return response;
  }

  console.log('🔧 Extracted HTML fragment length:', htmlFragment.length);
  console.log('🔧 Fragment starts with:', htmlFragment.substring(0, 100));
  console.log('🔧 Fragment includes userDashboard:', htmlFragment.includes('userDashboard'));

  // Check if the fragment already contains <div id="app"> - if so, wrap it in full HTML structure
  if (htmlFragment && htmlFragment.includes('<div id="app"')) {
    console.log('🔧 Fragment contains <div id="app">, creating full HTML document');
    return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Prototype</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
</head>
<body>
  ${htmlFragment}
</body>
</html>`;
  }

  // If it's a fragment (doesn't start with DOCTYPE or html), wrap it in parent structure
  if (htmlFragment && !htmlFragment.startsWith('<!DOCTYPE') && !htmlFragment.startsWith('<html')) {
    return createParentHtmlStructure(htmlFragment);
  }

  return htmlFragment;
};

// Create parent HTML structure with Tailwind CSS for fragments
const createParentHtmlStructure = (fragment: string): string => {
  return `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Prototype</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
  <script>
    // Event delegation for data-action attributes
    document.addEventListener('DOMContentLoaded', function() {
      document.addEventListener('click', function(e) {
        const target = e.target.closest('[data-action]');
        if (target) {
          const action = target.getAttribute('data-action');
          const targetId = target.getAttribute('data-target');

          switch(action) {
            case 'openModal':
              if (targetId) openModal(targetId);
              break;
            case 'closeModal':
              if (targetId) closeModal(targetId);
              break;
            default:
              console.log('Action triggered:', action, 'Target:', targetId);
          }
        }
      });
    });

    // Modal functions
    function openModal(modalId) {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.style.display = 'flex';
        document.body.style.overflow = 'hidden';
      }
    }

    function closeModal(modalId) {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = 'auto';
      }
    }
  </script>
</head>
<body>
  <div id="app">
    ${fragment}
  </div>
</body>
</html>`;
};

const addInteractionDetection = (html: string): string => {
  // Remove any existing scripts first
  let cleanHtml = html
    .replace(/<script[^>]*>[\s\S]*?<\/script>/gi, '')
    .replace(/<!-- INTERACTION_DETECTION_ADDED -->/g, '');

  const interactionScript = `
    <script>
      console.log('🔥 Interaction detection script loaded');

      // Add interaction detection after DOM is loaded
      document.addEventListener('DOMContentLoaded', function() {
        console.log('🔥 DOM loaded, setting up interaction detection');

        // Function to check if element is navigation
        function isNavigationElement(element) {
          const tagName = element.tagName.toLowerCase();
          const className = element.className || '';
          const id = element.id || '';

          // Check if this is a navigation link with data-nav attribute (from our linking system)
          if (element.hasAttribute('data-nav')) return true;

          // Check if element is a link in navigation context
          if (tagName === 'a') {
            // Check if the link itself has navigation-related classes/ids
            if (className.includes('nav') || className.includes('menu') ||
                id.includes('nav') || id.includes('menu')) return true;

            // Check parent elements for navigation context
            let parent = element.parentElement;
            while (parent && parent !== document.body) {
              const parentTag = parent.tagName.toLowerCase();
              const parentClass = parent.className || '';
              const parentId = parent.id || '';

              if (parentTag === 'nav' || parentTag === 'header') return true;
              if (parentClass.includes('nav') || parentClass.includes('menu')) return true;
              if (parentId.includes('nav') || parentId.includes('menu')) return true;

              parent = parent.parentElement;
            }
          }

          // Check if element itself is navigation container
          if (tagName === 'nav' || tagName === 'header') return true;
          if (className.includes('nav') || className.includes('menu')) return true;
          if (id.includes('nav') || id.includes('menu')) return true;

          return false;
        }

        // Function to check if element needs implementation
        function needsImplementation(element) {
          const tagName = element.tagName.toLowerCase();
          const className = element.className || '';
          const textContent = element.textContent?.trim() || '';

          // Check if element has any event listeners (more comprehensive check)
          const onclickAttr = element.getAttribute('onclick');
          const hasEventListeners = element.onclick ||
                                   onclickAttr ||
                                   element.getAttribute('data-bs-toggle') || // Bootstrap modals
                                   element.getAttribute('data-toggle') ||    // Bootstrap modals
                                   element.getAttribute('data-target') ||    // Bootstrap targets
                                   element.getAttribute('data-bs-target') || // Bootstrap 5 targets
                                   (element._listeners && Object.keys(element._listeners).length > 0); // jQuery/custom listeners

          // Check if onclick calls openModal function (Readdy.ai pattern)
          const callsOpenModal = onclickAttr && onclickAttr.includes('openModal(');

          // If it calls openModal, check if the modal exists and the function is defined
          if (callsOpenModal) {
            const modalIdMatch = onclickAttr.match(/openModal\(['"]([^'"]+)['"]\)/);
            if (modalIdMatch) {
              const modalId = modalIdMatch[1];
              const modalExists = document.getElementById(modalId);
              const functionExists = typeof window.openModal === 'function';

              if (modalExists && functionExists) {
                return { needs: false }; // Modal is properly implemented
              }
            }
          }

          // Interactive elements that might need implementation
          if (tagName === 'button' && !hasEventListeners) {
            // Check if button opens a modal by looking for nearby modal elements
            const buttonId = element.id;
            const buttonText = textContent.toLowerCase();

            // Check if there's a modal with matching ID or content
            const relatedModal = document.querySelector(\`#\${buttonId}Modal, #\${buttonId}-modal, [id*="\${buttonText}"], .modal\`);
            if (relatedModal) {
              return { needs: false }; // Modal exists, button is implemented
            }

            return { needs: true, type: 'button', reason: 'Button without click handler' };
          }

          if (tagName === 'a') {
            const href = element.href || element.getAttribute('href');
            if (!href || href === '#' || href === 'javascript:void(0)') {
              // Check if it's a modal trigger or has other functionality
              if (hasEventListeners) {
                return { needs: false };
              }
              return { needs: true, type: 'link', reason: 'Link without proper href' };
            }
          }

          if (tagName === 'form' && !element.onsubmit && !element.getAttribute('onsubmit') && !element.action) {
            return { needs: true, type: 'form', reason: 'Form without action or handler' };
          }

          // Check for elements with click-like classes but no handlers
          if (className.includes('btn') || className.includes('button') || className.includes('clickable')) {
            if (!hasEventListeners) {
              return { needs: true, type: 'interactive', reason: 'Clickable element without handler' };
            }
          }

          return { needs: false };
        }

        // Add click listeners only to potentially interactive elements
        document.addEventListener('click', function(event) {
          const element = event.target;
          console.log('🔥 Element clicked:', element);
          console.log('🔥 Element tag:', element.tagName);
          console.log('🔥 Element full text:', element.textContent);
          console.log('🔥 Element innerHTML:', element.innerHTML?.substring(0, 200));

          // Check if this element actually needs implementation
          const implementationCheck = needsImplementation(element);

          // Only proceed if element needs implementation or is navigation
          const isNav = isNavigationElement(element);

          console.log('🔥 Is navigation:', isNav);
          console.log('🔥 Needs implementation:', implementationCheck.needs);

          if (!implementationCheck.needs && !isNav) {
            console.log('🔥 Element does not need implementation, ignoring');
            return;
          }

          // Get the most specific text content for the clicked element
          let elementText = '';

          // Strategy 1: For links and buttons, get only the direct text content
          if (element.tagName.toLowerCase() === 'a' || element.tagName.toLowerCase() === 'button') {
            // Get only direct text nodes of this element, not child elements
            const directTextNodes = Array.from(element.childNodes)
              .filter(node => node.nodeType === Node.TEXT_NODE)
              .map(node => node.textContent?.trim())
              .filter(text => text && text.length > 0);

            if (directTextNodes.length > 0) {
              elementText = directTextNodes.join(' ');
            } else {
              // If no direct text nodes, get the text but limit to first meaningful text
              const fullText = element.textContent?.trim() || '';
              // Split by common separators and take the first meaningful part
              const parts = fullText.split(/[\n\r\t\|•·]/);
              elementText = parts[0]?.trim() || fullText;
            }
          } else {
            // Strategy 2: For other elements, get direct text nodes only
            const directText = Array.from(element.childNodes)
              .filter(node => node.nodeType === Node.TEXT_NODE)
              .map(node => node.textContent?.trim())
              .filter(text => text && text.length > 0)
              .join(' ');

            elementText = directText || element.textContent?.trim() || '';
          }

          // Strategy 3: If still empty or too long, try to get the most relevant text
          if (!elementText || elementText.length > 50) {
            const fullText = element.textContent?.trim() || '';
            if (fullText.length <= 50) {
              elementText = fullText;
            } else {
              // Take the first word/phrase that looks like a navigation item
              const words = fullText.split(/\s+/);
              elementText = words.slice(0, 3).join(' '); // Take first 3 words max
            }
          }

          // Fallback to element attributes if no text
          if (!elementText) {
            elementText = element.getAttribute('aria-label') ||
                         element.getAttribute('title') ||
                         element.getAttribute('alt') ||
                         element.tagName.toLowerCase();
          }

          // Get element information
          const elementInfo = {
            tagName: element.tagName,
            textContent: elementText,
            className: element.className || '',
            id: element.id || '',
            href: element.href || '',
            isNavigation: isNav,
            isInteractive: implementationCheck.needs,
            implementationType: implementationCheck.type,
            implementationReason: implementationCheck.reason,
            selector: getElementSelector(element)
          };

          console.log('🔥 Element needs attention:', elementInfo);
          console.log('🔥 Extracted text:', elementText);
          console.log('🔥 Text extraction details:', {
            tagName: element.tagName,
            fullText: element.textContent,
            extractedText: elementText,
            directTextNodes: Array.from(element.childNodes)
              .filter(node => node.nodeType === Node.TEXT_NODE)
              .map(node => node.textContent?.trim()),
            childElementsCount: element.children.length
          });

          // Send message to parent window
          if (window.parent && window.parent !== window) {
            window.parent.postMessage({
              type: 'ELEMENT_CLICKED',
              element: elementInfo
            }, '*');
          }

          // Prevent default for navigation elements and unimplemented interactive elements
          if (elementInfo.isNavigation || implementationCheck.needs) {
            event.preventDefault();
          }
        });

        function getElementSelector(element) {
          if (element.id) return '#' + element.id;
          if (element.className) {
            const classes = element.className.split(' ').filter(c => c.trim());
            if (classes.length > 0) return '.' + classes[0];
          }
          return element.tagName.toLowerCase();
        }

        // Add visual indicators for unimplemented elements
        function addUnimplementedIndicators() {
          // Remove existing indicators first
          document.querySelectorAll('.unimplemented-indicator').forEach(el => {
            el.classList.remove('unimplemented-indicator');
            const tooltip = el.querySelector('.unimplemented-tooltip');
            if (tooltip) tooltip.remove();
          });

          // Add CSS for indicators (only once)
          if (!document.getElementById('unimplemented-styles')) {
            const style = document.createElement('style');
            style.id = 'unimplemented-styles';
            style.textContent = \`
              .unimplemented-indicator {
                position: relative;
                cursor: pointer !important;
              }
              .unimplemented-indicator::after {
                content: '⚡';
                position: absolute;
                top: -8px;
                right: -8px;
                background: #ff6b35;
                color: white;
                border-radius: 50%;
                width: 16px;
                height: 16px;
                font-size: 10px;
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
                pointer-events: none;
                box-shadow: 0 2px 4px rgba(0,0,0,0.2);
              }
              .unimplemented-indicator:hover::after {
                background: #ff4500;
                transform: scale(1.1);
                transition: all 0.2s ease;
              }
              .unimplemented-tooltip {
                position: absolute;
                background: #333;
                color: white;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 12px;
                white-space: nowrap;
                z-index: 1001;
                pointer-events: none;
                opacity: 0;
                transition: opacity 0.2s ease;
              }
              .unimplemented-indicator:hover .unimplemented-tooltip {
                opacity: 1;
              }
            \`;
            document.head.appendChild(style);
          }

          // Find and mark unimplemented elements
          const allElements = document.querySelectorAll('*');
          allElements.forEach(element => {
            const check = needsImplementation(element);
            if (check.needs) {
              element.classList.add('unimplemented-indicator');

              // Add tooltip
              const tooltip = document.createElement('div');
              tooltip.className = 'unimplemented-tooltip';
              tooltip.textContent = check.reason;
              tooltip.style.top = '-30px';
              tooltip.style.left = '50%';
              tooltip.style.transform = 'translateX(-50%)';
              element.style.position = 'relative';
              element.appendChild(tooltip);
            }
          });
        }

        // Function to refresh indicators (called after content changes)
        window.refreshUnimplementedIndicators = addUnimplementedIndicators;

        // Debug function to test modal functionality
        window.debugModalFunctionality = function() {
          console.log('🔍 Debugging modal functionality...');

          // Find all buttons
          const buttons = document.querySelectorAll('button');
          buttons.forEach((button, index) => {
            const onclickAttr = button.getAttribute('onclick');
            console.log(\`Button \${index + 1}:\`, {
              text: button.textContent?.trim(),
              hasOnclick: !!button.onclick,
              onclickAttr: onclickAttr,
              hasDataToggle: !!button.getAttribute('data-bs-toggle') || !!button.getAttribute('data-toggle'),
              hasDataTarget: !!button.getAttribute('data-bs-target') || !!button.getAttribute('data-target'),
              className: button.className
            });

            // Test if onclick attribute works
            if (onclickAttr) {
              console.log(\`Button \${index + 1} onclick attribute: "\${onclickAttr}"\`);

              // Check if the function exists
              const functionName = onclickAttr.match(/(\w+)\(/)?.[1];
              if (functionName && typeof window[functionName] === 'function') {
                console.log(\`✅ Function \${functionName} exists and is callable\`);
              } else {
                console.error(\`❌ Function \${functionName} not found or not callable\`);
              }
            }
          });

          // Find all modals
          const modals = document.querySelectorAll('.modal, [id*="modal"], [class*="modal"]');
          console.log(\`Found \${modals.length} modal elements:\`, modals);
          modals.forEach((modal, index) => {
            console.log(\`Modal \${index + 1}:\`, {
              id: modal.id,
              className: modal.className,
              display: window.getComputedStyle(modal).display,
              visibility: window.getComputedStyle(modal).visibility
            });
          });

          // Check for JavaScript functions
          const modalFunctions = ['openModal', 'closeModal', 'showModal', 'hideModal'];
          modalFunctions.forEach(funcName => {
            if (typeof window[funcName] === 'function') {
              console.log(\`✅ Function \${funcName} is available\`);
            } else {
              console.log(\`❌ Function \${funcName} not found\`);
            }
          });
        };

        // Test modal functionality
        window.testModalOpen = function(modalId) {
          console.log(\`🧪 Testing modal open for: \${modalId}\`);
          const modal = document.getElementById(modalId);
          if (modal) {
            modal.style.display = 'block';
            console.log(\`✅ Modal \${modalId} opened successfully\`);

            // Auto-close after 3 seconds for testing
            setTimeout(() => {
              modal.style.display = 'none';
              console.log(\`✅ Modal \${modalId} closed automatically\`);
            }, 3000);
          } else {
            console.error(\`❌ Modal \${modalId} not found\`);
          }
        };

        // Manual test function to force add indicators
        window.testAddIndicators = function() {
          console.log('🧪 Manually testing indicator addition...');
          addUnimplementedIndicators();
          const indicators = document.querySelectorAll('.unimplemented-indicator');
          console.log(\`✅ Added indicators to \${indicators.length} elements\`);

          // List all buttons for debugging
          const buttons = document.querySelectorAll('button');
          console.log(\`🔍 Found \${buttons.length} buttons total:\`);
          buttons.forEach((btn, i) => {
            console.log(\`  Button \${i + 1}: "\${btn.textContent?.trim()}" - onclick: \${btn.getAttribute('onclick') || 'none'}\`);
          });
        };

        // Add indicators after a short delay to ensure DOM is ready
        setTimeout(() => {
          console.log('🔥 Running addUnimplementedIndicators...');
          addUnimplementedIndicators();
          console.log('🔥 Indicators added, checking for elements...');
          const indicators = document.querySelectorAll('.unimplemented-indicator');
          console.log('🔥 Found', indicators.length, 'elements with indicators');
        }, 500);

        // Watch for DOM changes and refresh indicators
        const observer = new MutationObserver(function(mutations) {
          let shouldRefresh = false;
          mutations.forEach(function(mutation) {
            if (mutation.type === 'childList' || mutation.type === 'attributes') {
              shouldRefresh = true;
            }
          });

          if (shouldRefresh) {
            // Debounce the refresh to avoid too many calls
            clearTimeout(window.indicatorRefreshTimeout);
            window.indicatorRefreshTimeout = setTimeout(addUnimplementedIndicators, 1000);
          }
        });

        observer.observe(document.body, {
          childList: true,
          subtree: true,
          attributes: true,
          attributeFilter: ['onclick', 'data-bs-toggle', 'data-toggle', 'data-target', 'data-bs-target']
        });
      });
    </script>
    <!-- INTERACTION_DETECTION_ADDED -->
  `;

  // Insert script before closing body tag, or at the end if no body tag
  if (cleanHtml.includes('</body>')) {
    cleanHtml = cleanHtml.replace('</body>', `${interactionScript}\n</body>`);
  } else {
    cleanHtml += interactionScript;
  }

  console.log('🔥 Interaction script injected, content length:', cleanHtml.length);
  console.log('🔥 Script includes indicators?', cleanHtml.includes('addUnimplementedIndicators'));

  return cleanHtml;
};

const ensureCompleteHtml = (content: string): string => {
  if (!content.includes('<!DOCTYPE html')) {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Generated Content</title>
</head>
<body>
    ${content}
</body>
</html>`;
  }
  return content;
};

// ============================================================================
// MAIN HOOK
// ============================================================================

export interface EditorContext {
  projectId?: number;
  userId?: number;
  sessionId?: string;
}

export function useEditorV3(context: EditorContext = {}) {
  // ============================================================================
  // STATE
  // ============================================================================

  const [state, setState] = useState<EditorState>({
    // Content state
    htmlContent: '',
    streamingContent: '',
    stableIframeContent: '',

    // UI state
    viewMode: 'preview',
    isGenerating: false,
    isLinking: false,
    operationProgress: null,

    // Multi-page state
    pages: [
      { id: 'main', name: 'Main Page', content: '', isActive: true, lastUpdated: new Date() }
    ],
    currentPageId: 'main',

    // Chat state
    messages: [
      {
        role: 'assistant',
        content: 'Hi! I\'m your AI design assistant. Describe what you\'d like to create and I\'ll build it for you.',
        timestamp: new Date()
      }
    ],
    input: '',

    // Selection state
    selectedElement: null,
    showImplementModal: false,
    customFunctionality: ''
  });

  const iframeRef = useRef<HTMLIFrameElement>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const patchManagerRef = useRef<PatchManager | null>(null);

  // ============================================================================
  // ACTIONS
  // ============================================================================

  const updateState = useCallback((updates: Partial<EditorState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const setHtmlContent = useCallback((content: string) => {
    updateState({ htmlContent: content });
  }, [updateState]);

  const setStreamingContent = useCallback((content: string) => {
    updateState({ streamingContent: content });
  }, [updateState]);

  const setStableIframeContent = useCallback((content: string) => {
    updateState({ stableIframeContent: content });
  }, [updateState]);

  const setViewMode = useCallback((mode: ViewMode) => {
    updateState({ viewMode: mode });
  }, [updateState]);

  const setIsGenerating = useCallback((generating: boolean) => {
    updateState({ isGenerating: generating });
  }, [updateState]);

  const setIsLinking = useCallback((linking: boolean) => {
    updateState({ isLinking: linking });
  }, [updateState]);

  const addPage = useCallback((page: Omit<Page, 'lastUpdated'>) => {
    const newPage = { ...page, lastUpdated: new Date() };
    setState(prev => ({
      ...prev,
      pages: [
        // Keep existing pages as-is (no active flag management)
        ...prev.pages,
        // Add new page
        newPage
      ]
    }));
  }, []);

  const updatePage = useCallback((pageId: string, updates: Partial<Page>) => {
    setState(prev => ({
      ...prev,
      pages: prev.pages.map(page => {
        if (page.id === pageId) {
          return { ...page, ...updates, lastUpdated: new Date() };
        }
        return page;
      })
    }));
  }, []);

  const switchToPage = useCallback((pageId: string) => {
    setState(prev => {
      const page = prev.pages.find(p => p.id === pageId);
      if (page) {
        return {
          ...prev,
          currentPageId: pageId,
          htmlContent: extractHtmlFromResponse(page.content),
          stableIframeContent: page.content,
          // No active flag management - just update content and currentPageId
          pages: prev.pages
        };
      }
      return prev;
    });
  }, []);

  const reloadCurrentPage = useCallback(async (sessionId?: string) => {
    const currentSessionId = sessionId || state.currentPageId;
    if (!currentSessionId) {
      console.warn('⚠️ No session ID available for page reload');
      return false;
    }

    try {
      console.log('🔄 Reloading page content from database for session:', currentSessionId);

      // Import getSession dynamically to avoid circular dependencies
      const { getSession } = await import('../services/pageGenService');
      const response = await getSession(currentSessionId);

      if (response.success && response.session) {
        console.log('✅ Page content reloaded successfully');
        console.log('📄 Reloaded HTML length:', response.session.page_html?.length);

        // Update the HTML content in the editor
        setState(prev => ({
          ...prev,
          htmlContent: response.session.page_html,
          stableIframeContent: response.session.page_html
        }));

        return true;
      } else {
        console.error('❌ Failed to reload page content:', response);
        return false;
      }
    } catch (error) {
      console.error('❌ Error reloading page content:', error);
      return false;
    }
  }, [state.currentPageId]);

  const addMessage = useCallback((message: ChatMessage) => {
    setState(prev => ({
      ...prev,
      messages: [...prev.messages, message]
    }));
  }, []);

  const setInput = useCallback((input: string) => {
    updateState({ input });
  }, [updateState]);

  const clearInput = useCallback(() => {
    updateState({ input: '' });
  }, [updateState]);

  const setSelectedElement = useCallback((element: ElementInfo | null) => {
    updateState({ selectedElement: element });
  }, [updateState]);

  const setShowImplementModal = useCallback((show: boolean) => {
    updateState({ showImplementModal: show });
  }, [updateState]);

  const setCustomFunctionality = useCallback((functionality: string) => {
    updateState({ customFunctionality: functionality });
  }, [updateState]);

  // ============================================================================
  // CORE OPERATIONS
  // ============================================================================

  const generateFromPrompt = useCallback(async (prompt: string, pageTitle?: string) => {
    console.log('🚀 generateFromPrompt called!');
    console.log('🚀 Prompt:', prompt.substring(0, 100) + '...');

    // SAFETY CHECK: Prevent any auto-generation on page load
    if (!prompt || prompt.trim().length === 0) {
      console.log('🚀 Empty prompt, skipping generation');
      return;
    }

    // Prevent duplicate calls
    if (state.isGenerating) {
      console.log('🚀 Already generating, skipping');
      return;
    }

    console.log('🚀 Starting generation - user explicitly requested it');

    setIsGenerating(true);
    setStreamingContent('');

    // Track generation timing and auto-save state
    const generationStartTime = Date.now();
    const GENERATION_TIMEOUT = 300000; // 5 minutes
    const FALLBACK_SAVE_DELAY = 10000; // 10 seconds after generation completes

    let timeoutId: NodeJS.Timeout | null = null;
    let fallbackSaveTimeoutId: NodeJS.Timeout | null = null;
    let generationCompleted = false;
    let autoSaveReceived = false;
    let finalHtmlContent = '';

    try {
      const response = await fetch('/api/llm/v3/generate-html', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          prompt,
          projectId: context.projectId,
          pageTitle: pageTitle || extractTitleFromPrompt(prompt)
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) throw new Error('No response body');

      // Set up timeout for the entire generation process
      timeoutId = setTimeout(() => {
        console.warn('⏰ Generation timeout reached, attempting graceful handling');

        addMessage({
          role: 'assistant',
          content: '⏰ Generation is taking longer than expected. The page may still be created in the background. Please check the page list in a moment.',
          timestamp: new Date()
        });

        // Trigger fallback save check if we have content but no auto-save event
        if (!autoSaveReceived && finalHtmlContent.trim()) {
          triggerFallbackSave();
        }
      }, GENERATION_TIMEOUT);

      let accumulatedContent = '';
      let isCollectingHTML = false;
      let currentEvent = '';

      // Fallback save function
      const triggerFallbackSave = async () => {
        console.log('🔄 Triggering fallback save check...');

        try {
          // Dispatch a custom event to trigger page list refresh
          window.dispatchEvent(new CustomEvent('generationCompleted', {
            detail: {
              pageTitle: pageTitle || extractTitleFromPrompt(prompt),
              generatedHTML: finalHtmlContent,
              fallback: true
            }
          }));

          addMessage({
            role: 'assistant',
            content: '🔄 Checking if your page was created successfully...',
            timestamp: new Date()
          });
        } catch (error) {
          console.error('❌ Fallback save check failed:', error);
        }
      };

      while (true) {
        const { done, value } = await reader.read();
        if (done) {
          generationCompleted = true;
          console.log('📝 Generation stream completed');

          // Set up fallback save check if no auto-save event received
          if (!autoSaveReceived && finalHtmlContent.trim()) {
            fallbackSaveTimeoutId = setTimeout(() => {
              if (!autoSaveReceived) {
                console.log('🔄 No auto-save event received, triggering fallback save check');
                triggerFallbackSave();
              }
            }, FALLBACK_SAVE_DELAY);
          }
          break;
        }

        const chunk = new TextDecoder().decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('event:')) {
            currentEvent = line.substring(6).trim();
            if (currentEvent === 'start') {
              isCollectingHTML = true;
            } else if (currentEvent === 'end') {
              isCollectingHTML = false;
              // Only extract HTML if we were collecting HTML content (not diff data)
              if (accumulatedContent && !accumulatedContent.includes('shouldUseDiff')) {
                const cleanHtml = extractHtmlFromResponse(accumulatedContent);
                setHtmlContent(cleanHtml);
                finalHtmlContent = cleanHtml; // Store for fallback
              }
              setStreamingContent('');
            } else if (currentEvent === 'diff') {
              // Handle diff event - stop collecting HTML and process diff data
              isCollectingHTML = false;
            } else if (currentEvent === 'page_saved') {
              // Handle auto-save completion event
              isCollectingHTML = false;
            } else if (currentEvent === 'page_save_error') {
              // Handle auto-save error event
              isCollectingHTML = false;
            } else if (currentEvent === 'page_save_skipped') {
              // Handle auto-save skipped event
              isCollectingHTML = false;
            }
          } else if (line.startsWith('data:')) {
            const data = line.substring(5);

            // Check if this is diff data
            if (data.trim().startsWith('{') && data.includes('shouldUseDiff')) {
              try {
                const diffData = JSON.parse(data);
                console.log('🔧 Processing diff data in useEditorV3:', diffData);

                // Process the diff using the standalone PatchManager
                if (patchManagerRef.current && diffData.shouldUseDiff) {
                  const currentHtml = state.htmlContent || '';
                  const patchedHtml = await (patchManagerRef.current as any).applyUnifiedDiff(currentHtml, diffData.patches);

                  console.log('🔧 Diff applied successfully via standalone PatchManager:', {
                    originalLength: currentHtml.length,
                    patchedLength: patchedHtml.length,
                    hasChanges: patchedHtml !== currentHtml
                  });

                  setHtmlContent(patchedHtml);
                  finalHtmlContent = patchedHtml; // Store for fallback
                } else if (diffData.fallbackHtml) {
                  // Use fallback HTML if diff processing fails
                  setHtmlContent(diffData.fallbackHtml);
                  finalHtmlContent = diffData.fallbackHtml; // Store for fallback
                } else {
                  console.warn('🔧 PatchManager not available for diff processing');
                }

                setStreamingContent('');
                accumulatedContent = ''; // Clear accumulated content
              } catch (error) {
                console.error('🔧 Error processing diff data:', error);
                // Fall back to treating as regular content
                if (isCollectingHTML) {
                  accumulatedContent += data;
                  setStreamingContent(accumulatedContent);
                }
              }
            } else if (isCollectingHTML) {
              // Regular HTML content
              accumulatedContent += data;
              setStreamingContent(accumulatedContent);
            }

            // Handle auto-save events - check if we're processing a page_saved event
            if (currentEvent === 'page_saved' && data.trim().startsWith('{')) {
              try {
                const saveData = JSON.parse(data);
                console.log('✅ Auto-save completed:', saveData);
                autoSaveReceived = true;

                // Clear fallback timeout since we received the save event
                if (fallbackSaveTimeoutId) {
                  clearTimeout(fallbackSaveTimeoutId);
                  fallbackSaveTimeoutId = null;
                }

                // Dispatch custom event for page save completion
                window.dispatchEvent(new CustomEvent('pageSaved', {
                  detail: saveData
                }));
              } catch (e) {
                console.error('Failed to parse page save data:', e);
              }
            } else if (currentEvent === 'page_save_error') {
              console.error('❌ Auto-save failed:', data);
              autoSaveReceived = true; // Mark as received to prevent fallback

              // Dispatch custom event for page save error
              window.dispatchEvent(new CustomEvent('pageSaveError', {
                detail: { error: data }
              }));
            } else if (currentEvent === 'page_save_skipped') {
              console.warn('⚠️ Auto-save skipped:', data);
              autoSaveReceived = true; // Mark as received to prevent fallback

              // Dispatch custom event for page save skip
              window.dispatchEvent(new CustomEvent('pageSaveSkipped', {
                detail: { reason: data }
              }));
            }
          }
        }
      }

      addMessage({
        role: 'assistant',
        content: 'I\'ve created your design! What would you like to modify?',
        timestamp: new Date()
      });

    } catch (error) {
      console.error('Generation error:', error);

      // Check if this is a timeout error
      const isTimeout = error.message.includes('timeout') ||
                       (Date.now() - generationStartTime) >= GENERATION_TIMEOUT;

      if (isTimeout) {
        addMessage({
          role: 'assistant',
          content: '⏰ Generation timed out. Your page may still be created in the background. Please check the page list or try again.',
          timestamp: new Date()
        });
      } else {
        addMessage({
          role: 'assistant',
          content: 'Sorry, I encountered an error. Please try again.',
          timestamp: new Date()
        });
      }
    } finally {
      // Clean up timeouts
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      if (fallbackSaveTimeoutId) {
        clearTimeout(fallbackSaveTimeoutId);
      }

      setIsGenerating(false);
    }
  }, [state.isGenerating, setIsGenerating, setStreamingContent, setHtmlContent, addMessage]);

  // Helper function to properly extract #app content with nested divs
  const extractAppContent = (html: string): string | null => {
    const appStartMatch = html.match(/<div id="app"[^>]*>/);
    if (!appStartMatch) return null;

    const startIndex = appStartMatch.index! + appStartMatch[0].length;
    let depth = 1;
    let currentIndex = startIndex;

    // Find the matching closing </div> by counting nested divs
    while (currentIndex < html.length && depth > 0) {
      const nextDiv = html.indexOf('<div', currentIndex);
      const nextCloseDiv = html.indexOf('</div>', currentIndex);

      if (nextCloseDiv === -1) break; // No more closing divs

      if (nextDiv !== -1 && nextDiv < nextCloseDiv) {
        // Found opening div before closing div
        depth++;
        currentIndex = nextDiv + 4;
      } else {
        // Found closing div
        depth--;
        if (depth === 0) {
          // Found the matching closing div for #app
          const content = html.substring(appStartMatch.index!, nextCloseDiv + 6);
          console.log('🔍 Successfully extracted complete #app content:', {
            totalLength: content.length,
            preview: content.substring(0, 200) + '...'
          });
          return content;
        }
        currentIndex = nextCloseDiv + 6;
      }
    }

    console.warn('🔍 Could not find matching closing div for #app');
    return null;
  };

  const editContent = useCallback(async (prompt: string, additionalMessages: ChatMessage[] = [], elementSelector?: string, implementationType?: string, selectedElementData?: any) => {
    console.log('🔥 editContent called!');
    console.log('🔥 Call stack:', new Error().stack);
    console.log('🔥 Prompt:', prompt.substring(0, 100) + '...');

    // SAFETY CHECK: Prevent any auto-editing on page load
    if (!prompt || prompt.trim().length === 0) {
      console.log('🔥 Empty prompt, skipping edit');
      return;
    }

    // Prioritize stableIframeContent as it contains the complete, processed HTML
    // Fall back to htmlContent only if stableIframeContent is not available
    const currentContent = state.stableIframeContent || state.htmlContent;

    console.log('🔍 Edit content selection:', {
      hasStableContent: !!state.stableIframeContent,
      hasHtmlContent: !!state.htmlContent,
      stableLength: state.stableIframeContent?.length || 0,
      htmlLength: state.htmlContent?.length || 0,
      selectedContent: currentContent === state.stableIframeContent ? 'stable' : 'html',
      hasSelectedElement: !!state.selectedElement,
      selectedElementHTML: state.selectedElement?.outerHTML?.substring(0, 100) + '...'
    });

    if (!currentContent) {
      console.log('🔥 No current content, calling generateFromPrompt');
      await generateFromPrompt(prompt);
      return;
    }

    // Extract the #app content if we're sending a complete HTML document
    // The LLM expects to work with the inner content, not the full document
    let contentToSend = currentContent;
    if (currentContent.includes('<!DOCTYPE html') || currentContent.includes('<html')) {
      // Extract content from #app div or body using proper nested div matching
      const appMatch = extractAppContent(currentContent);
      const bodyMatch = currentContent.match(/<body[^>]*>([\s\S]*)<\/body>/);

      if (appMatch) {
        contentToSend = appMatch;
        console.log('🔍 Extracted #app content for editing:', {
          originalLength: currentContent.length,
          extractedLength: contentToSend.length,
          type: 'app-div'
        });
      } else if (bodyMatch && bodyMatch[1]) {
        contentToSend = bodyMatch[1].trim();
        console.log('🔍 Extracted body content for editing:', {
          originalLength: currentContent.length,
          extractedLength: contentToSend.length,
          type: 'body-content'
        });
      } else {
        console.warn('🔍 Could not extract clean content, using full HTML');
      }
    }

    // Extract fragment HTML from selected element if available
    let fragmentHtml = contentToSend; // Default to full content

    // Use passed selectedElementData or fallback to state
    const elementData = selectedElementData || state.selectedElement;

    console.log('🔍 Selected element debug:', {
      hasSelectedElement: !!elementData,
      elementTag: elementData?.tagName,
      elementText: elementData?.textContent,
      hasOuterHTML: !!elementData?.outerHTML,
      outerHTMLLength: elementData?.outerHTML?.length || 0,
      outerHTMLPreview: elementData?.outerHTML?.substring(0, 100) + '...',
      dataSource: selectedElementData ? 'passed' : 'state'
    });

    console.log('🔍 Content debug:', {
      currentContentLength: currentContent.length,
      currentContentPreview: currentContent.substring(0, 200) + '...',
      contentToSendLength: contentToSend.length,
      contentToSendPreview: contentToSend.substring(0, 200) + '...',
      selectedElementInContent: currentContent.includes(elementData?.textContent || ''),
      selectedElementInContentToSend: contentToSend.includes(elementData?.textContent || '')
    });

    if (elementData?.outerHTML && elementData.outerHTML.trim().length > 0) {
      fragmentHtml = elementData.outerHTML;
      console.log('🔍 Using selected element as fragment:', {
        elementTag: elementData.tagName,
        elementText: elementData.textContent,
        fragmentLength: fragmentHtml.length
      });
    } else {
      // Try to reconstruct element HTML from available data
      if (elementData) {
        const { tagName, textContent, attributes } = elementData;
        if (tagName && textContent) {
          // Build basic element HTML
          const attrs = Object.entries(attributes || {})
            .map(([key, value]) => `${key}="${value}"`)
            .join(' ');
          fragmentHtml = `<${tagName.toLowerCase()}${attrs ? ' ' + attrs : ''}>${textContent}</${tagName.toLowerCase()}>`;
          console.log('🔍 Reconstructed element HTML:', {
            reconstructed: fragmentHtml,
            originalTag: tagName,
            originalText: textContent
          });
        } else {
          console.warn('🔍 No outerHTML and insufficient data to reconstruct element, using full content');
        }
      }
    }

    setIsGenerating(true);
    setStreamingContent('');

    try {
      // Include both existing messages and any additional messages (like intent)
      const fullConversationHistory = [...state.messages, ...additionalMessages];

      // Use current page ID if no sessionId provided in context
      const pageIdToUse = context.sessionId || state.currentPageId;

      console.log('🔧 Frontend context:', {
        projectId: context.projectId,
        sessionId: context.sessionId,
        currentPageId: state.currentPageId,
        pageIdToUse,
        hasPageId: !!pageIdToUse
      });

      // 🚀 COMPREHENSIVE REQUEST: Include all targeting parameters
      const requestBody: any = {
        prompt,
        conversationHistory: fullConversationHistory, // Include full conversation history (ALWAYS SENT)
        projectId: context.projectId, // Include project ID for version tracking
        sessionId: pageIdToUse // Include page ID for database saving (use current page if no context sessionId)
      };

      // Add pageId for database lookup (optimization)
      if (pageIdToUse) {
        requestBody.pageId = pageIdToUse;
        console.log('🚀 [Optimization] Sending pageId for database lookup:', pageIdToUse);
      }

      // Add targeting parameters (always include when available)
      if (elementSelector) {
        requestBody.elementSelector = elementSelector;
        console.log('🎯 [Targeting] Element selector:', elementSelector);
      }

      if (implementationType) {
        requestBody.implementationType = implementationType;
        console.log('🎯 [Targeting] Implementation type:', implementationType);
      }

      // Add element context from selectedElementData
      if (selectedElementData) {
        requestBody.elementContext = selectedElementData;
        requestBody.targetPosition = selectedElementData.targetPosition || 'replace';
        console.log('🎯 [Targeting] Element context provided');
      }

      // 🚀 OPTIMIZATION: Smart HTML content handling
      if (pageIdToUse) {
        // Use pageId optimization, but still send fragment for targeting
        if (fragmentHtml && fragmentHtml !== contentToSend) {
          requestBody.fragmentHtml = fragmentHtml; // Specific element for targeting
          console.log('🎯 [Hybrid] Using pageId + fragment for optimal targeting');
        }
        console.log('💾 [Optimization] Using pageId - major bandwidth savings');
        console.log('💰 [Optimization] HTML content saved:', contentToSend?.length || 0, 'chars');
      } else {
        // Fallback: send full HTML content
        requestBody.htmlContent = contentToSend; // Full HTML for context
        requestBody.fragmentHtml = fragmentHtml; // Specific element to edit
        console.log('📏 [Fallback] No pageId available - sending HTML content:', contentToSend?.length || 0, 'chars');
      }

      console.log('📡 Sending comprehensive edit request:', {
        sessionId: requestBody.sessionId,
        pageId: requestBody.pageId,
        hasHtmlContent: !!requestBody.htmlContent,
        htmlContentLength: requestBody.htmlContent?.length || 0,
        fragmentHtmlLength: requestBody.fragmentHtml?.length || 0,
        conversationHistoryLength: fullConversationHistory?.length || 0,
        elementSelector: requestBody.elementSelector,
        implementationType: requestBody.implementationType,
        hasElementContext: !!requestBody.elementContext,
        targetPosition: requestBody.targetPosition,
        optimizationUsed: !!requestBody.pageId && !requestBody.htmlContent
      });

      const response = await fetch('/api/llm/v3/edit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const reader = response.body?.getReader();
      if (!reader) throw new Error('No response body');

      let accumulatedContent = '';
      let isCollectingHTML = false;
      let currentEvent = '';

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = new TextDecoder().decode(value);
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('event:')) {
            currentEvent = line.substring(6).trim();
            if (currentEvent === 'start') {
              isCollectingHTML = true;
            } else if (currentEvent === 'end') {
              isCollectingHTML = false;
              // Only extract HTML if we were collecting HTML content (not diff data)
              if (accumulatedContent && !accumulatedContent.includes('shouldUseDiff')) {
                const cleanHtml = extractHtmlFromResponse(accumulatedContent);
                setHtmlContent(cleanHtml);
              }
              setStreamingContent('');
            } else if (currentEvent === 'context') {
              // Handle contextual understanding message
              isCollectingHTML = false; // Don't collect this as HTML
            } else if (currentEvent === 'progress') {
              // Progress event detected - data will come in next data: line
            } else if (currentEvent === 'diff') {
              // Handle diff event - stop collecting HTML and process diff data
              isCollectingHTML = false;
            }
          } else if (line.startsWith('data:')) {
            const data = line.substring(5);

            // Check if this is diff data (same logic as generateFromPrompt)
            if (data.trim().startsWith('{') && data.includes('shouldUseDiff')) {
              try {
                const diffData = JSON.parse(data);
                console.log('🔧 Processing diff data in editContent:', diffData);

                // Process the diff using the standalone PatchManager
                if (patchManagerRef.current && diffData.shouldUseDiff) {
                  // CRITICAL FIX: Apply patch to the correct content
                  // If this is a fragment edit, apply patch to the fragment, then reconstruct
                  let targetContent = currentContent || '';
                  let isFragmentPatch = false;

                  if (diffData.selector && elementData?.outerHTML) {
                    // This is a fragment edit - apply patch to the fragment content
                    targetContent = elementData.outerHTML;
                    isFragmentPatch = true;
                    console.log('🔧 Fragment patch detected - applying to fragment:', {
                      fragmentLength: targetContent.length,
                      selector: diffData.selector,
                      fullContentLength: currentContent.length
                    });
                    console.log('🔍 Fragment content preview:', targetContent.substring(0, 100) + '...');
                    console.log('🔍 Fragment contains "Add":', targetContent.includes('Add'));
                    console.log('🔍 Fragment contains "Task":', targetContent.includes('Task'));
                  }

                  const patchedContent = await (patchManagerRef.current as any).applyUnifiedDiff(targetContent, diffData.patches);

                  console.log('🔧 Edit diff applied successfully via standalone PatchManager:', {
                    originalLength: targetContent.length,
                    patchedLength: patchedContent.length,
                    hasChanges: patchedContent !== targetContent,
                    isFragmentPatch
                  });

                  let finalHtml = patchedContent;

                  if (isFragmentPatch && elementData?.outerHTML) {
                    // Replace the fragment in the full HTML
                    finalHtml = currentContent.replace(elementData.outerHTML, patchedContent);
                    console.log('🔧 Fragment replaced in full HTML:', {
                      fullHtmlLength: finalHtml.length,
                      fragmentReplaced: true,
                      originalFragment: elementData.outerHTML.substring(0, 50) + '...',
                      patchedFragment: patchedContent.substring(0, 50) + '...'
                    });
                  } else if (currentContent.includes('<!DOCTYPE html') && !patchedContent.includes('<!DOCTYPE html')) {
                    // Reconstruct full HTML if we extracted content earlier
                    finalHtml = ensureCompleteHtml(patchedContent);
                    console.log('🔍 Reconstructed complete HTML after patching');
                  }

                  setHtmlContent(finalHtml);
                } else if (diffData.fallbackHtml) {
                  // Use fallback HTML if diff processing fails
                  setHtmlContent(diffData.fallbackHtml);
                } else {
                  console.warn('🔧 PatchManager not available for edit diff processing');
                }

                setStreamingContent('');
                accumulatedContent = ''; // Clear accumulated content
              } catch (error) {
                console.error('🔧 Error processing edit diff data:', error);
                // Fall back to treating as regular content
                if (isCollectingHTML) {
                  accumulatedContent += data;
                  setStreamingContent(accumulatedContent);
                }
              }
            } else if (currentEvent === 'context') {
              // Handle context data
              const contextData = data.trim();
              if (contextData) {
                // Add contextual understanding message to chat
                addMessage({
                  role: 'assistant',
                  content: contextData,
                  timestamp: new Date()
                });
              }
            } else if (isCollectingHTML) {
              // Handle HTML content data
              accumulatedContent += data;
              setStreamingContent(accumulatedContent);
            }
          }
        }
      }

      addMessage({
        role: 'assistant',
        content: 'I\'ve updated your design. What would you like to change next?',
        timestamp: new Date()
      });

    } catch (error) {
      console.error('Edit error:', error);
      addMessage({
        role: 'assistant',
        content: 'Sorry, I encountered an error. Please try again.',
        timestamp: new Date()
      });
    } finally {
      setIsGenerating(false);
    }
  }, [state.stableIframeContent, state.htmlContent, generateFromPrompt, setIsGenerating, setStreamingContent, setHtmlContent, addMessage]);

  const linkAllPages = useCallback(async () => {
    const pagesWithContent = state.pages.filter(p => p.content && p.content.length > 0);
    if (pagesWithContent.length < 2) return;

    setIsLinking(true);

    try {
      for (const page of pagesWithContent) {
        const otherPageNames = pagesWithContent
          .filter(p => p.id !== page.id)
          .map(p => p.name);

        const prompt = `Update the navigation bar to include links to: ${otherPageNames.join(', ')}

🎯 REQUIREMENTS:
- Keep existing design exactly the same
- Add proper <a> tags for each page link
- Maintain responsive behavior
- Preserve all other content`;

        const response = await fetch('/api/llm/v3/edit', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          credentials: 'include',
          body: JSON.stringify({
            htmlContent: page.content,
            fragmentHtml: page.content, // Fragment to edit (same as htmlContent for linking)
            prompt,
            projectId: context.projectId, // Include project ID for version tracking
            sessionId: page.id // Use the actual page ID for linking operations
          })
        });

        if (response.ok) {
          // Process streaming response and update page content
          const reader = response.body?.getReader();
          if (reader) {
            let accumulatedContent = '';
            let isCollectingHTML = false;

            while (true) {
              const { done, value } = await reader.read();
              if (done) break;

              const chunk = new TextDecoder().decode(value);
              const lines = chunk.split('\n');

              for (const line of lines) {
                if (line.startsWith('event:')) {
                  const event = line.substring(6);
                  if (event === 'start') {
                    isCollectingHTML = true;
                  } else if (event === 'end') {
                    isCollectingHTML = false;
                    // Update page content with linked navigation
                    const cleanHtml = extractHtmlFromResponse(accumulatedContent);
                    if (cleanHtml) {
                      setState(prev => ({
                        ...prev,
                        pages: prev.pages.map(p =>
                          p.id === page.id
                            ? { ...p, content: cleanHtml, lastUpdated: new Date() }
                            : p
                        )
                      }));
                    }
                  }
                } else if (line.startsWith('data:') && isCollectingHTML) {
                  const data = line.substring(5);
                  accumulatedContent += data;
                }
              }
            }
          }
        }

        // No delay needed for 2-page linking - process immediately
      }
    } catch (error) {
      console.error('Linking error:', error);
    } finally {
      setIsLinking(false);
    }
  }, [state.pages, setIsLinking]);

  // ============================================================================
  // EFFECTS
  // ============================================================================

  // Initialize PatchManager
  useEffect(() => {
    patchManagerRef.current = new PatchManager();
    console.log('🔧 PatchManager initialized in useEditorV3');

    return () => {
      patchManagerRef.current = null;
    };
  }, []);

  // Update stable iframe content immediately when htmlContent changes
  useEffect(() => {
    const cleanContent = extractHtmlFromResponse(state.htmlContent);
    if (cleanContent && cleanContent.length > 50) {
      const completeHtml = ensureCompleteHtml(cleanContent);
      setState(prev => ({
        ...prev,
        stableIframeContent: completeHtml,
        pages: prev.pages.map(page =>
          page.id === prev.currentPageId
            ? { ...page, content: completeHtml, lastUpdated: new Date() }
            : page
        )
      }));
    }
  }, [state.htmlContent, state.currentPageId]);

  // Auto-scroll messages
  useEffect(() => {
    if (!state.isGenerating) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
  }, [state.messages, state.isGenerating]);

  // ============================================================================
  // RETURN
  // ============================================================================

  const actions: EditorActions = {
    setHtmlContent,
    setStreamingContent,
    setStableIframeContent,
    setViewMode,
    setIsGenerating,
    setIsLinking,
    addPage,
    updatePage,
    switchToPage,
    reloadCurrentPage,
    addMessage,
    setInput,
    clearInput,
    setSelectedElement,
    setShowImplementModal,
    setCustomFunctionality,
    generateFromPrompt,
    editContent,
    linkAllPages
  };

  return {
    state,
    actions,
    refs: {
      iframeRef,
      messagesEndRef
    },
    utils: {
      extractHtmlFromResponse,
      addInteractionDetection,
      ensureCompleteHtml
    }
  };
}
