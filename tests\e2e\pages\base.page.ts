import { Page, Locator, expect } from '@playwright/test';

/**
 * Base Page Object Model class
 * 
 * Provides common functionality and utilities for all page objects
 * in the JustPrototype application.
 */
export class BasePage {
  protected page: Page;
  
  constructor(page: Page) {
    this.page = page;
  }
  
  /**
   * Navigate to a specific path
   */
  async goto(path: string = '/') {
    await this.page.goto(path);
    await this.waitForPageLoad();
  }
  
  /**
   * Wait for page to be fully loaded
   */
  async waitForPageLoad() {
    await this.page.waitForLoadState('networkidle');
    await this.page.waitForLoadState('domcontentloaded');
  }
  
  /**
   * Wait for an element to be visible
   */
  async waitForElement(selector: string, timeout: number = 30000): Promise<Locator> {
    const element = this.page.locator(selector);
    await element.waitFor({ state: 'visible', timeout });
    return element;
  }
  
  /**
   * Wait for text to appear on the page
   */
  async waitForText(text: string, timeout: number = 30000) {
    await this.page.waitForFunction(
      (searchText) => document.body.innerText.includes(searchText),
      text,
      { timeout }
    );
  }
  
  /**
   * Take a screenshot with a descriptive name
   */
  async takeScreenshot(name: string) {
    await this.page.screenshot({
      path: `../reports/screenshots/${name}-${Date.now()}.png`,
      fullPage: true
    });
  }
  
  /**
   * Get the current URL
   */
  async getCurrentUrl(): Promise<string> {
    return this.page.url();
  }
  
  /**
   * Check if an element exists (without waiting)
   */
  async elementExists(selector: string): Promise<boolean> {
    try {
      const element = this.page.locator(selector);
      await element.waitFor({ state: 'attached', timeout: 1000 });
      return true;
    } catch {
      return false;
    }
  }
  
  /**
   * Scroll to an element
   */
  async scrollToElement(selector: string) {
    const element = this.page.locator(selector);
    await element.scrollIntoViewIfNeeded();
  }
  
  /**
   * Wait for a network request to complete
   */
  async waitForNetworkRequest(urlPattern: string | RegExp, timeout: number = 30000) {
    return this.page.waitForRequest(urlPattern, { timeout });
  }
  
  /**
   * Wait for a network response
   */
  async waitForNetworkResponse(urlPattern: string | RegExp, timeout: number = 30000) {
    return this.page.waitForResponse(urlPattern, { timeout });
  }
  
  /**
   * Get console logs
   */
  getConsoleLogs(): string[] {
    return this.page.context().pages()[0].evaluate(() => {
      // This would need to be implemented with proper console capture
      return [];
    });
  }
  
  /**
   * Clear browser storage
   */
  async clearStorage() {
    await this.page.evaluate(() => {
      localStorage.clear();
      sessionStorage.clear();
    });
  }
  
  /**
   * Set local storage item
   */
  async setLocalStorage(key: string, value: string) {
    await this.page.evaluate(
      ({ key, value }) => localStorage.setItem(key, value),
      { key, value }
    );
  }
  
  /**
   * Get local storage item
   */
  async getLocalStorage(key: string): Promise<string | null> {
    return this.page.evaluate(
      (key) => localStorage.getItem(key),
      key
    );
  }
  
  /**
   * Wait for element to contain specific text
   */
  async waitForElementText(selector: string, text: string, timeout: number = 30000) {
    const element = this.page.locator(selector);
    await expect(element).toContainText(text, { timeout });
  }
  
  /**
   * Click and wait for navigation
   */
  async clickAndWaitForNavigation(selector: string) {
    await Promise.all([
      this.page.waitForNavigation(),
      this.page.click(selector)
    ]);
  }
  
  /**
   * Fill form field and wait for any validation
   */
  async fillField(selector: string, value: string) {
    const field = this.page.locator(selector);
    await field.fill(value);
    // Wait a bit for any validation to trigger
    await this.page.waitForTimeout(500);
  }
  
  /**
   * Get element text content
   */
  async getElementText(selector: string): Promise<string> {
    const element = this.page.locator(selector);
    return element.textContent() || '';
  }
  
  /**
   * Check if element is visible
   */
  async isElementVisible(selector: string): Promise<boolean> {
    try {
      const element = this.page.locator(selector);
      await element.waitFor({ state: 'visible', timeout: 5000 });
      return true;
    } catch {
      return false;
    }
  }
  
  /**
   * Wait for loading to complete (generic loading indicators)
   */
  async waitForLoadingComplete() {
    // Wait for common loading indicators to disappear
    const loadingSelectors = [
      '[data-testid="loading"]',
      '.loading',
      '.spinner',
      '[aria-label*="loading"]',
      '[aria-label*="Loading"]'
    ];
    
    for (const selector of loadingSelectors) {
      try {
        const element = this.page.locator(selector);
        await element.waitFor({ state: 'hidden', timeout: 5000 });
      } catch {
        // Ignore if loading indicator doesn't exist
      }
    }
  }
  
  /**
   * Retry an action with exponential backoff
   */
  async retryAction<T>(
    action: () => Promise<T>,
    maxRetries: number = 3,
    baseDelay: number = 1000
  ): Promise<T> {
    let lastError: Error;
    
    for (let attempt = 0; attempt < maxRetries; attempt++) {
      try {
        return await action();
      } catch (error) {
        lastError = error as Error;
        
        if (attempt < maxRetries - 1) {
          const delay = baseDelay * Math.pow(2, attempt);
          await this.page.waitForTimeout(delay);
        }
      }
    }
    
    throw lastError!;
  }
}
