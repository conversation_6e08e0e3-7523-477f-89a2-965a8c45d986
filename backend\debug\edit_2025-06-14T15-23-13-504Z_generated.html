<div id="app">
  <nav class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <h1 class="text-xl font-semibold text-gray-900 mr-8">Call Center</h1>
          </div>
          <div class="flex space-x-2">
            <button data-nav="dashboard" class="border-blue-500 text-blue-600 whitespace-nowrap py-2 px-4 mx-1 border-b-2 font-medium text-sm">Dashboard</button>
            <button data-nav="calls" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-4 mx-1 border-b-2 font-medium text-sm transition-colors duration-200 rounded-t-lg hover:bg-gray-50">Calls</button>
            <button data-nav="analytics" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-4 mx-1 border-b-2 font-medium text-sm transition-colors duration-200 rounded-t-lg hover:bg-gray-50">Analytics</button>
            <button data-nav="transcriptions" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-4 mx-1 border-b-2 font-medium text-sm transition-colors duration-200 rounded-t-lg hover:bg-gray-50">Transcriptions</button>
          </div>
        </div>
        <div class="flex items-center">
          <div class="text-sm text-gray-500">
            <i class="fas fa-user-circle mr-1"></i>
            Agent
          </div>
        </div>
      </div>
    </div>
  </nav>

  <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <section data-view="dashboard">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Call Dashboard</h2>
        <div class="flex space-x-3">
          <button id="new-call-btn" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors">
            <i class="fas fa-phone mr-2"></i>New Call
          </button>
          <button id="call-history-btn" class="bg-white hover:bg-gray-50 border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium transition-colors">
            <i class="fas fa-history mr-2"></i>History
          </button>
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Active Calls</h3>
            <span class="bg-blue-100 text-blue-800 text-xs font-semibold px-2.5 py-0.5 rounded">Live</span>
          </div>
          <div class="mt-4">
            <div class="text-3xl font-bold text-gray-900">4</div>
            <div class="mt-2 text-sm text-gray-500">+2 from yesterday</div>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Average Duration</h3>
          <div class="mt-4">
            <div class="text-3xl font-bold text-gray-900">5:32</div>
            <div class="mt-2 text-sm text-gray-500">+0:45 from last week</div>
          </div>
        </div>

        <div class="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Satisfaction</h3>
          <div class="mt-4">
            <div class="text-3xl font-bold text-gray-900">87%</div>
            <div class="mt-2 text-sm text-gray-500">+3% from last month</div>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Recent Calls</h3>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Call ID</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#C-78945</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Sarah Johnson</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">4:23</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Completed</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <button data-action="view-transcript" data-target="transcript-78945" class="text-blue-600 hover:text-blue-900 mr-3">Transcript</button>
                  <button data-action="play-recording" data-target="recording-78945" class="text-blue-600 hover:text-blue-900">Play</button>
                </td>
              </tr>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#C-78944</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Michael Chen</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">6:12</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Completed</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <button data-action="view-transcript" data-target="transcript-78944" class="text-blue-600 hover:text-blue-900 mr-3">Transcript</button>
                  <button data-action="play-recording" data-target="recording-78944" class="text-blue-600 hover:text-blue-900">Play</button>
                </td>
              </tr>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">#C-78943</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">David Wilson</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">3:45</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">In Progress</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <button data-action="join-call" data-target="call-78943" class="text-blue-600 hover:text-blue-900 mr-3">Join</button>
                  <button disabled class="text-gray-400 cursor-not-allowed">Transcript</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>

    <section data-view="transcriptions" class="hidden">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-gray-800">Call Transcriptions</h2>
        <div class="flex space-x-3">
          <button id="filter-transcripts-btn" class="bg-white hover:bg-gray-50 border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium transition-colors">
            <i class="fas fa-filter mr-2"></i>Filter
          </button>
          <button id="export-transcripts-btn" class="bg-white hover:bg-gray-50 border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium transition-colors">
            <i class="fas fa-download mr-2"></i>Export
          </button>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
          <div class="flex items-center">
            <input type="text" placeholder="Search transcripts..." class="border border-gray-300 rounded-md px-3 py-2 text-sm w-64">
          </div>
          <div class="flex items-center space-x-2">
            <span class="text-sm text-gray-500">Sort by:</span>
            <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
              <option>Newest first</option>
              <option>Oldest first</option>
              <option>Longest duration</option>
              <option>Shortest duration</option>
            </select>
          </div>
        </div>
        <div class="divide-y divide-gray-200">
          <div class="p-6 hover:bg-gray-50 transition-colors">
            <div class="flex justify-between items-start">
              <div>
                <h3 class="text-lg font-medium text-gray-900">#C-78945 - Sarah Johnson</h3>
                <p class="text-sm text-gray-500 mt-1">Duration: 4:23 • 12/15/2023 10:45 AM</p>
              </div>
              <div class="flex space-x-2">
                <button data-action="view-transcript" data-target="transcript-78945" class="text-blue-600 hover:text-blue-900 text-sm font-medium">View Full</button>
                <button data-action="play-recording" data-target="recording-78945" class="text-blue-600 hover:text-blue-900 text-sm font-medium">Play Audio</button>
              </div>
            </div>
            <div class="mt-4 bg-gray-50 p-4 rounded-md">
              <div class="flex mb-3">
                <span class="font-medium text-gray-700 mr-2">Agent:</span>
                <p class="text-gray-600">Hello, this is Mark from customer support. How can I help you today?</p>
              </div>
              <div class="flex">
                <span class="font-medium text-gray-700 mr-2">Customer:</span>
                <p class="text-gray-600">Hi Mark, I'm having trouble with my recent order. The tracking shows delivered but I never received it.</p>
              </div>
              <div class="text-sm text-blue-600 mt-2">+ Show more (12 lines)</div>
            </div>
          </div>
          <div class="p-6 hover:bg-gray-50 transition-colors">
            <div class="flex justify-between items-start">
              <div>
                <h3 class="text-lg font-medium text-gray-900">#C-78944 - Michael Chen</h3>
                <p class="text-sm text-gray-500 mt-1">Duration: 6:12 • 12/15/2023 9:30 AM</p>
              </div>
              <div class="flex space-x-2">
                <button data-action="view-transcript" data-target="transcript-78944" class="text-blue-600 hover:text-blue-900 text-sm font-medium">View Full</button>
                <button data-action="play-recording" data-target="recording-78944" class="text-blue-600 hover:text-blue-900 text-sm font-medium">Play Audio</button>
              </div>
            </div>
            <div class="mt-4 bg-gray-50 p-4 rounded-md">
              <div class="flex mb-3">
                <span class="font-medium text-gray-700 mr-2">Agent:</span>
                <p class="text-gray-600">Good morning, this is Lisa speaking. How may I assist you today?</p>
              </div>
              <div class="flex">
                <span class="font-medium text-gray-700 mr-2">Customer:</span>
                <p class="text-gray-600">Hello Lisa, I need help with a billing question. I was charged twice for my subscription this month.</p>
              </div>
              <div class="text-sm text-blue-600 mt-2">+ Show more (18 lines)</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- New Call Modal -->
    <div id="new-call-modal" class="fixed z-50 inset-0 overflow-y-auto hidden">
      <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
          <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Start New Call</h3>
            <div class="space-y-4">
              <div>
                <label for="call-number" class="block text-sm font-medium text-gray-700">Phone Number</label>
                <input type="text" id="call-number" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
              </div>
              <div>
                <label for="call-purpose" class="block text-sm font-medium text-gray-700">Purpose</label>
                <select id="call-purpose" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                  <option>Customer Support</option>
                  <option>Sales Inquiry</option>
                  <option>Technical Support</option>
                  <option>Billing Question</option>
                  <option>Other</option>
                </select>
              </div>
              <div>
                <label for="call-notes" class="block text-sm font-medium text-gray-700">Notes (Optional)</label>
                <textarea id="call-notes" rows="3" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"></textarea>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button type="button" id="start-call-btn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
              Start Call
            </button>
            <button type="button" data-action="closeModal" data-target="new-call-modal" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Transcript Modal -->
    <div id="transcript-modal" class="fixed z-50 inset-0 overflow-y-auto hidden">
      <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
          <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="flex justify-between items-start">
              <h3 class="text-lg leading-6 font-medium text-gray-900" id="transcript-title">Call Transcript</h3>
              <button data-action="closeModal" data-target="transcript-modal" class="text-gray-400 hover:text-gray-500">
                <span class="sr-only">Close</span>
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div class="mt-4">
              <div class="bg-gray-50 p-4 rounded-md max-h-96 overflow-y-auto">
                <div id="transcript-content" class="space-y-4">
                  <!-- Transcript content will be inserted here -->
                </div>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button type="button" id="download-transcript-btn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
              Download Transcript
            </button>
            <button type="button" data-action="closeModal" data-target="transcript-modal" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
              Close
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Call Player Modal -->
    <div id="call-player-modal" class="fixed z-50 inset-0 overflow-y-auto hidden">
      <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
          <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
        </div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="flex justify-between items-start">
              <h3 class="text-lg leading-6 font-medium text-gray-900" id="call-player-title">Call Recording</h3>
              <button data-action="closeModal" data-target="call-player-modal" class="text-gray-400 hover:text-gray-500">
                <span class="sr-only">Close</span>
                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
            <div class="mt-4">
              <div class="bg-gray-50 p-4 rounded-md">
                <div class="flex items-center justify-center">
                  <div class="w-full max-w-md">
                    <div class="flex items-center justify-between mb-4">
                      <span id="current-time" class="text-sm text-gray-500">0:00</span>
                      <span id="duration" class="text-sm text-gray-500">4:23</span>
                    </div>
                    <div class="relative h-2 bg-gray-200 rounded-full mb-4">
                      <div id="progress-bar" class="absolute h-2 bg-blue-600 rounded-full" style="width: 0%"></div>
                    </div>
                    <div class="flex items-center justify-center space-x-6">
                      <button id="rewind-btn" class="text-gray-700 hover:text-blue-600">
                        <svg class="h-8 w-8" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M8.445 14.832A1 1 0 0010 14v-2.798l5.445 3.63A1 1 0 0017 14V6a1 1 0 00-1.555-.832L10 8.798V6a1 1 0 00-1.555-.832l-6 4a1 1 0 000 1.664l6 4z" />
                        </svg>
                      </button>
                      <button id="play-btn" class="bg-blue-600 text-white rounded-full p-3 hover:bg-blue-700">
                        <svg class="h-8 w-8" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" />
                        </svg>
                      </button>
                      <button id="forward-btn" class="text-gray-700 hover:text-blue-600">
                        <svg class="h-8 w-8" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M4.555 5.168A1 1 0 003 6v8a1 1 0 001.555.832L10 11.202V14a1 1 0 001.555.832l6-4a1 1 0 000-1.664l-6-4A1 1 0 0010 6v2.798L4.555 5.168z" />
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button type="button" id="download-recording-btn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
              Download Recording
            </button>
            <button type="button" data-action="closeModal" data-target="call-player-modal" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  </main>
</div>