/**
 * Prompt Enhancement Service
 * 
 * Enhances user prompts with detailed instructions before sending to LLM
 * to prevent misinterpretation and improve editing accuracy.
 */

export interface PromptEnhancement {
  originalPrompt: string;
  enhancedPrompt: string;
  analysisType: 'addition' | 'replacement' | 'modification' | 'creation';
  gridChanges?: string;
  preservationNotes?: string;
  confidence: 'high' | 'medium' | 'low';
}

export interface EnhancementRequest {
  prompt: string;
  htmlContext?: string;
  elementContext?: string;
}

export interface EnhancementResponse {
  success: boolean;
  enhancement?: PromptEnhancement;
  originalPrompt?: string;
  error?: string;
}

/**
 * Enhance user prompt with detailed instructions
 */
export async function enhancePrompt(request: EnhancementRequest): Promise<EnhancementResponse> {
  try {
    console.log('🔧 Enhancing prompt:', request.prompt);
    
    const response = await fetch('/api/llm/v3/enhance-prompt', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({
        prompt: request.prompt,
        htmlContext: request.htmlContext || '',
        elementContext: request.elementContext || ''
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    if (data.success) {
      console.log('✅ Prompt enhanced successfully');
      console.log('📊 Analysis type:', data.enhancement.analysisType);
      console.log('🎯 Enhanced prompt preview:', data.enhancement.enhancedPrompt?.substring(0, 100) + '...');
    }
    
    return data;
  } catch (error: any) {
    console.error('❌ Error enhancing prompt:', error);
    return {
      success: false,
      error: error.message || 'Failed to enhance prompt'
    };
  }
}

/**
 * Enhanced editing workflow that uses prompt enhancement
 */
export async function enhancedEdit(
  htmlContent: string,
  userPrompt: string,
  elementSelector?: string,
  options: any = {}
): Promise<any> {
  try {
    console.log('🚀 Starting enhanced editing workflow');
    
    // Step 1: Enhance the prompt
    const enhancementResult = await enhancePrompt({
      prompt: userPrompt,
      htmlContext: htmlContent.substring(0, 2000), // First 2KB for context
      elementContext: elementSelector || ''
    });

    if (!enhancementResult.success) {
      console.warn('⚠️ Prompt enhancement failed, using original prompt');
      // Fall back to original prompt if enhancement fails
      return editWithOriginalPrompt(htmlContent, userPrompt, elementSelector, options);
    }

    const enhancement = enhancementResult.enhancement!;
    
    // Step 2: Use enhanced prompt for editing
    console.log('🎯 Using enhanced prompt for editing');
    console.log('📊 Analysis type:', enhancement.analysisType);
    console.log('🔧 Grid changes:', enhancement.gridChanges || 'None');
    console.log('💾 Preservation notes:', enhancement.preservationNotes || 'None');
    
    return editWithEnhancedPrompt(htmlContent, enhancement.enhancedPrompt, elementSelector, {
      ...options,
      enhancementMetadata: {
        originalPrompt: enhancement.originalPrompt,
        analysisType: enhancement.analysisType,
        confidence: enhancement.confidence,
        gridChanges: enhancement.gridChanges,
        preservationNotes: enhancement.preservationNotes
      }
    });
    
  } catch (error) {
    console.error('❌ Enhanced editing workflow failed:', error);
    // Fall back to original prompt on any error
    return editWithOriginalPrompt(htmlContent, userPrompt, elementSelector, options);
  }
}

/**
 * Edit with enhanced prompt
 */
async function editWithEnhancedPrompt(
  htmlContent: string,
  enhancedPrompt: string,
  elementSelector?: string,
  options: any = {}
): Promise<any> {
  console.log('🎨 Editing with enhanced prompt');
  
  const response = await fetch('/api/llm/v3/edit', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify({
      htmlContent,
      prompt: enhancedPrompt,
      elementSelector,
      ...options
    }),
  });

  return response;
}

/**
 * Fallback: Edit with original prompt
 */
async function editWithOriginalPrompt(
  htmlContent: string,
  originalPrompt: string,
  elementSelector?: string,
  options: any = {}
): Promise<any> {
  console.log('🔄 Falling back to original prompt');
  
  const response = await fetch('/api/llm/v3/edit', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify({
      htmlContent,
      prompt: originalPrompt,
      elementSelector,
      ...options
    }),
  });

  return response;
}

/**
 * Preview enhancement without executing edit
 */
export async function previewEnhancement(
  userPrompt: string,
  htmlContext?: string
): Promise<PromptEnhancement | null> {
  try {
    const result = await enhancePrompt({
      prompt: userPrompt,
      htmlContext: htmlContext?.substring(0, 1000) || ''
    });
    
    return result.success ? result.enhancement! : null;
  } catch (error) {
    console.error('❌ Error previewing enhancement:', error);
    return null;
  }
}

/**
 * Batch enhance multiple prompts
 */
export async function batchEnhancePrompts(
  prompts: string[],
  htmlContext?: string
): Promise<PromptEnhancement[]> {
  const results = await Promise.allSettled(
    prompts.map(prompt => enhancePrompt({
      prompt,
      htmlContext: htmlContext?.substring(0, 1000) || ''
    }))
  );
  
  return results
    .filter((result): result is PromiseFulfilledResult<EnhancementResponse> => 
      result.status === 'fulfilled' && result.value.success
    )
    .map(result => result.value.enhancement!);
}
