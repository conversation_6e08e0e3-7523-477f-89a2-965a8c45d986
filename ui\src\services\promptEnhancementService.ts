/**
 * Prompt Enhancement Service
 * 
 * Enhances user prompts with detailed instructions before sending to LLM
 * to prevent misinterpretation and improve editing accuracy.
 */

export interface PromptEnhancement {
  originalPrompt: string;
  enhancedPrompt: string;
  analysisType: 'addition' | 'replacement' | 'modification' | 'creation';
  gridChanges?: string;
  preservationNotes?: string;
  confidence: 'high' | 'medium' | 'low';
}

export interface EnhancementRequest {
  prompt: string;
  htmlContext?: string;
  elementContext?: string;
  pageId?: string; // For database optimization
}

export interface EnhancementResponse {
  success: boolean;
  enhancement?: PromptEnhancement;
  originalPrompt?: string;
  error?: string;
}

/**
 * Enhance user prompt with detailed instructions
 */
export async function enhancePrompt(request: EnhancementRequest): Promise<EnhancementResponse> {
  try {
    console.log('🔧 Enhancing prompt:', request.prompt);
    
    const response = await fetch('/api/llm/v3/enhance-prompt', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      credentials: 'include',
      body: JSON.stringify({
        prompt: request.prompt,
        htmlContext: request.htmlContext || '',
        elementContext: request.elementContext || '',
        pageId: request.pageId // For database optimization
      }),
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    if (data.success) {
      console.log('✅ Prompt enhanced successfully');
      console.log('📊 Analysis type:', data.enhancement.analysisType);
      console.log('🎯 Enhanced prompt preview:', data.enhancement.enhancedPrompt?.substring(0, 100) + '...');
    }
    
    return data;
  } catch (error: any) {
    console.error('❌ Error enhancing prompt:', error);
    return {
      success: false,
      error: error.message || 'Failed to enhance prompt'
    };
  }
}

/**
 * Enhanced editing workflow that uses prompt enhancement
 */
export async function enhancedEdit(
  htmlContent: string,
  userPrompt: string,
  elementSelector?: string,
  options: any = {}
): Promise<any> {
  try {
    console.log('🚀 Starting enhanced editing workflow');
    
    // Step 1: Enhance the prompt
    const enhancementResult = await enhancePrompt({
      prompt: userPrompt,
      htmlContext: htmlContent.substring(0, 2000), // First 2KB for context
      elementContext: elementSelector || ''
    });

    if (!enhancementResult.success) {
      console.warn('⚠️ Prompt enhancement failed, using original prompt');
      // Fall back to original prompt if enhancement fails
      return editWithOriginalPrompt(htmlContent, userPrompt, elementSelector, options);
    }

    const enhancement = enhancementResult.enhancement!;
    
    // Step 2: Use enhanced prompt for editing
    console.log('🎯 Using enhanced prompt for editing');
    console.log('📊 Analysis type:', enhancement.analysisType);
    console.log('🔧 Grid changes:', enhancement.gridChanges || 'None');
    console.log('💾 Preservation notes:', enhancement.preservationNotes || 'None');
    
    return editWithEnhancedPrompt(htmlContent, enhancement.enhancedPrompt, elementSelector, {
      ...options,
      enhancementMetadata: {
        originalPrompt: enhancement.originalPrompt,
        analysisType: enhancement.analysisType,
        confidence: enhancement.confidence,
        gridChanges: enhancement.gridChanges,
        preservationNotes: enhancement.preservationNotes
      }
    });
    
  } catch (error) {
    console.error('❌ Enhanced editing workflow failed:', error);
    // Fall back to original prompt on any error
    return editWithOriginalPrompt(htmlContent, userPrompt, elementSelector, options);
  }
}

/**
 * Edit with enhanced prompt
 */
async function editWithEnhancedPrompt(
  htmlContent: string,
  enhancedPrompt: string,
  elementSelector?: string,
  options: any = {}
): Promise<any> {
  console.log('🎨 Editing with enhanced prompt');
  
  const response = await fetch('/api/llm/v3/edit', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify({
      htmlContent,
      prompt: enhancedPrompt,
      elementSelector,
      ...options
    }),
  });

  return response;
}

/**
 * Fallback: Edit with original prompt
 */
async function editWithOriginalPrompt(
  htmlContent: string,
  originalPrompt: string,
  elementSelector?: string,
  options: any = {}
): Promise<any> {
  console.log('🔄 Falling back to original prompt');
  
  const response = await fetch('/api/llm/v3/edit', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    credentials: 'include',
    body: JSON.stringify({
      htmlContent,
      prompt: originalPrompt,
      elementSelector,
      ...options
    }),
  });

  return response;
}

/**
 * Preview enhancement without executing edit
 */
export async function previewEnhancement(
  userPrompt: string,
  htmlContext?: string,
  pageId?: string
): Promise<PromptEnhancement | null> {
  try {
    // 🚀 OPTIMIZATION: Use pageId if available, otherwise use htmlContext
    const request: EnhancementRequest = {
      prompt: userPrompt
    };

    if (pageId) {
      request.pageId = pageId;
      console.log('💾 [Enhancement Optimization] Using pageId for context:', pageId);
    } else if (htmlContext) {
      request.htmlContext = htmlContext.substring(0, 1000);
      console.log('📏 [Enhancement Fallback] Using HTML context:', request.htmlContext.length, 'chars');
    }

    const result = await enhancePrompt(request);

    return result.success ? result.enhancement! : null;
  } catch (error) {
    console.error('❌ Error previewing enhancement:', error);
    return null;
  }
}

/**
 * Batch enhance multiple prompts
 */
export async function batchEnhancePrompts(
  prompts: string[],
  htmlContext?: string,
  pageId?: string
): Promise<PromptEnhancement[]> {
  const results = await Promise.allSettled(
    prompts.map(prompt => {
      const request: EnhancementRequest = { prompt };

      if (pageId) {
        request.pageId = pageId;
      } else if (htmlContext) {
        request.htmlContext = htmlContext.substring(0, 1000);
      }

      return enhancePrompt(request);
    })
  );

  return results
    .filter((result): result is PromiseFulfilledResult<EnhancementResponse> =>
      result.status === 'fulfilled' && result.value.success
    )
    .map(result => result.value.enhancement!);
}
