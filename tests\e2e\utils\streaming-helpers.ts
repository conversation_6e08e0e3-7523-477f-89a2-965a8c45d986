import { Page, Route } from '@playwright/test';

/**
 * Streaming Response Testing Utilities
 * 
 * Provides utilities for testing Server-Sent Events (SSE) and streaming
 * responses in the JustPrototype application.
 */

export interface StreamingEvent {
  event: string;
  data: string;
  timestamp: number;
}

export interface MockStreamingResponse {
  events: StreamingEvent[];
  totalDuration: number;
  chunkDelay: number;
}

/**
 * Mock streaming response generator for LLM APIs
 */
export class StreamingMockGenerator {
  
  /**
   * Generate a mock streaming response for prototype generation
   */
  static generatePrototypeStream(prompt: string): MockStreamingResponse {
    const events: StreamingEvent[] = [];
    let timestamp = 0;
    
    // Start event
    events.push({
      event: 'start',
      data: '',
      timestamp: timestamp
    });
    
    // Generate HTML content based on prompt
    const htmlContent = this.generateMockHTML(prompt);
    const chunks = this.chunkHTML(htmlContent, 50); // 50 chars per chunk
    
    // Data events
    chunks.forEach((chunk, index) => {
      timestamp += 100; // 100ms between chunks
      events.push({
        event: 'data',
        data: chunk,
        timestamp: timestamp
      });
    });
    
    // End event
    timestamp += 100;
    events.push({
      event: 'end',
      data: '',
      timestamp: timestamp
    });
    
    return {
      events,
      totalDuration: timestamp,
      chunkDelay: 100
    };
  }
  
  /**
   * Generate a mock streaming response for editing
   */
  static generateEditStream(editPrompt: string, originalHTML: string): MockStreamingResponse {
    const events: StreamingEvent[] = [];
    let timestamp = 0;
    
    // Start event
    events.push({
      event: 'start',
      data: '',
      timestamp: timestamp
    });
    
    // Generate edited HTML
    const editedHTML = this.generateEditedHTML(originalHTML, editPrompt);
    const chunks = this.chunkHTML(editedHTML, 30); // Smaller chunks for edits
    
    // Data events
    chunks.forEach((chunk, index) => {
      timestamp += 80; // Faster for edits
      events.push({
        event: 'data',
        data: chunk,
        timestamp: timestamp
      });
    });
    
    // End event
    timestamp += 80;
    events.push({
      event: 'end',
      data: '',
      timestamp: timestamp
    });
    
    return {
      events,
      totalDuration: timestamp,
      chunkDelay: 80
    };
  }
  
  /**
   * Generate mock HTML based on prompt keywords
   */
  private static generateMockHTML(prompt: string): string {
    const lowerPrompt = prompt.toLowerCase();
    
    if (lowerPrompt.includes('coffee shop') || lowerPrompt.includes('coffee')) {
      return this.getCoffeeShopHTML();
    } else if (lowerPrompt.includes('dashboard') || lowerPrompt.includes('admin')) {
      return this.getDashboardHTML();
    } else if (lowerPrompt.includes('landing page') || lowerPrompt.includes('landing')) {
      return this.getLandingPageHTML();
    } else {
      return this.getGenericHTML(prompt);
    }
  }
  
  /**
   * Generate edited HTML based on edit prompt
   */
  private static generateEditedHTML(originalHTML: string, editPrompt: string): string {
    const lowerEdit = editPrompt.toLowerCase();
    
    if (lowerEdit.includes('contact form')) {
      return originalHTML.replace(
        '</div>',
        `
        <div class="contact-form bg-white p-6 rounded-lg shadow-md">
          <h3 class="text-xl font-bold mb-4">Contact Us</h3>
          <form>
            <input type="text" placeholder="Name" class="w-full p-2 mb-3 border rounded">
            <input type="email" placeholder="Email" class="w-full p-2 mb-3 border rounded">
            <textarea placeholder="Message" class="w-full p-2 mb-3 border rounded h-24"></textarea>
            <button type="submit" class="bg-blue-600 text-white px-4 py-2 rounded">Send</button>
          </form>
        </div>
        </div>`
      );
    } else if (lowerEdit.includes('blue') || lowerEdit.includes('color')) {
      return originalHTML
        .replace(/bg-red-/g, 'bg-blue-')
        .replace(/text-red-/g, 'text-blue-')
        .replace(/border-red-/g, 'border-blue-');
    } else {
      // Generic edit - just add a modification indicator
      return originalHTML.replace(
        '<div',
        '<div data-edited="true"'
      );
    }
  }
  
  /**
   * Split HTML into chunks for streaming
   */
  private static chunkHTML(html: string, chunkSize: number): string[] {
    const chunks: string[] = [];
    for (let i = 0; i < html.length; i += chunkSize) {
      chunks.push(html.substring(i, i + chunkSize));
    }
    return chunks;
  }
  
  /**
   * Coffee shop HTML template
   */
  private static getCoffeeShopHTML(): string {
    return `
<div id="app" class="min-h-screen bg-gradient-to-br from-amber-50 to-orange-100">
  <header class="bg-amber-900 text-white p-4">
    <div class="container mx-auto flex justify-between items-center">
      <h1 class="text-2xl font-bold">Coffee Paradise</h1>
      <nav>
        <a href="#menu" class="mx-2 hover:text-amber-200">Menu</a>
        <a href="#about" class="mx-2 hover:text-amber-200">About</a>
        <a href="#contact" class="mx-2 hover:text-amber-200">Contact</a>
      </nav>
    </div>
  </header>
  
  <main>
    <section class="hero bg-amber-800 text-white py-20">
      <div class="container mx-auto text-center">
        <h2 class="text-5xl font-bold mb-4">Welcome to Coffee Paradise</h2>
        <p class="text-xl mb-8">Experience the finest coffee in town</p>
        <button class="bg-amber-600 hover:bg-amber-700 px-8 py-3 rounded-lg text-lg font-semibold">
          Order Now
        </button>
      </div>
    </section>
    
    <section id="menu" class="py-16">
      <div class="container mx-auto">
        <h3 class="text-3xl font-bold text-center mb-12">Our Menu</h3>
        <div class="grid md:grid-cols-3 gap-8">
          <div class="bg-white p-6 rounded-lg shadow-md">
            <h4 class="text-xl font-bold mb-2">Espresso</h4>
            <p class="text-gray-600 mb-4">Rich and bold coffee shot</p>
            <span class="text-2xl font-bold text-amber-800">$3.50</span>
          </div>
          <div class="bg-white p-6 rounded-lg shadow-md">
            <h4 class="text-xl font-bold mb-2">Cappuccino</h4>
            <p class="text-gray-600 mb-4">Espresso with steamed milk foam</p>
            <span class="text-2xl font-bold text-amber-800">$4.25</span>
          </div>
          <div class="bg-white p-6 rounded-lg shadow-md">
            <h4 class="text-xl font-bold mb-2">Latte</h4>
            <p class="text-gray-600 mb-4">Smooth espresso with steamed milk</p>
            <span class="text-2xl font-bold text-amber-800">$4.75</span>
          </div>
        </div>
      </div>
    </section>
  </main>
  
  <footer class="bg-amber-900 text-white p-8">
    <div class="container mx-auto text-center">
      <p>&copy; 2024 Coffee Paradise. All rights reserved.</p>
    </div>
  </footer>
</div>`;
  }
  
  /**
   * Dashboard HTML template
   */
  private static getDashboardHTML(): string {
    return `
<div id="app" class="min-h-screen bg-gray-100">
  <header class="bg-blue-600 text-white p-4">
    <div class="container mx-auto">
      <h1 class="text-2xl font-bold">Admin Dashboard</h1>
    </div>
  </header>
  
  <main class="container mx-auto p-6">
    <div class="grid md:grid-cols-4 gap-6 mb-8">
      <div class="bg-white p-6 rounded-lg shadow">
        <h3 class="text-lg font-semibold text-gray-600">Total Users</h3>
        <p class="text-3xl font-bold text-blue-600">1,234</p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow">
        <h3 class="text-lg font-semibold text-gray-600">Revenue</h3>
        <p class="text-3xl font-bold text-green-600">$12,345</p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow">
        <h3 class="text-lg font-semibold text-gray-600">Orders</h3>
        <p class="text-3xl font-bold text-orange-600">567</p>
      </div>
      <div class="bg-white p-6 rounded-lg shadow">
        <h3 class="text-lg font-semibold text-gray-600">Growth</h3>
        <p class="text-3xl font-bold text-purple-600">+23%</p>
      </div>
    </div>
    
    <div class="bg-white p-6 rounded-lg shadow">
      <h3 class="text-xl font-bold mb-4">Recent Activity</h3>
      <div class="space-y-3">
        <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
          <span>New user registered</span>
          <span class="text-sm text-gray-500">2 minutes ago</span>
        </div>
        <div class="flex justify-between items-center p-3 bg-gray-50 rounded">
          <span>Order #1234 completed</span>
          <span class="text-sm text-gray-500">5 minutes ago</span>
        </div>
      </div>
    </div>
  </main>
</div>`;
  }
  
  /**
   * Generic landing page HTML template
   */
  private static getLandingPageHTML(): string {
    return `
<div id="app" class="min-h-screen">
  <header class="bg-blue-600 text-white p-4">
    <div class="container mx-auto flex justify-between items-center">
      <h1 class="text-2xl font-bold">Your Brand</h1>
      <nav>
        <a href="#features" class="mx-2 hover:text-blue-200">Features</a>
        <a href="#pricing" class="mx-2 hover:text-blue-200">Pricing</a>
        <a href="#contact" class="mx-2 hover:text-blue-200">Contact</a>
      </nav>
    </div>
  </header>
  
  <main>
    <section class="hero bg-gradient-to-r from-blue-600 to-purple-600 text-white py-20">
      <div class="container mx-auto text-center">
        <h2 class="text-5xl font-bold mb-4">Welcome to Our Platform</h2>
        <p class="text-xl mb-8">The best solution for your business needs</p>
        <button class="bg-white text-blue-600 px-8 py-3 rounded-lg text-lg font-semibold hover:bg-gray-100">
          Get Started
        </button>
      </div>
    </section>
  </main>
</div>`;
  }
  
  /**
   * Generic HTML template
   */
  private static getGenericHTML(prompt: string): string {
    return `
<div id="app" class="min-h-screen bg-gray-50">
  <header class="bg-gray-800 text-white p-4">
    <div class="container mx-auto">
      <h1 class="text-2xl font-bold">Generated Content</h1>
    </div>
  </header>
  
  <main class="container mx-auto p-6">
    <div class="bg-white p-8 rounded-lg shadow">
      <h2 class="text-3xl font-bold mb-4">Content Based on Your Prompt</h2>
      <p class="text-gray-600 mb-6">Prompt: "${prompt}"</p>
      <div class="space-y-4">
        <p>This is generated content based on your request.</p>
        <button class="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700">
          Action Button
        </button>
      </div>
    </div>
  </main>
</div>`;
  }
}
