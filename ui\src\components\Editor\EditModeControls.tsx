/**
 * Edit Mode Controls Component
 * Provides prominent UI controls for element selection and edit mode
 * Keeps the main editor page clean and modular
 */

import React from 'react';
import { ElementSelection } from '../../services/elementSelectorService';

interface EditModeControlsProps {
  isEditModeActive: boolean;
  selectedElement: ElementSelection | null;
  onToggleEditMode: () => void;
  onClearSelection: () => void;
  onExitEditMode: () => void;
}

const EditModeControls: React.FC<EditModeControlsProps> = ({
  isEditModeActive,
  selectedElement,
  onToggleEditMode,
  onClearSelection,
  onExitEditMode
}) => {
  if (!isEditModeActive && !selectedElement) {
    return (
      <div className="flex items-center space-x-2">
        <button
          onClick={onToggleEditMode}
          className="px-3 py-1.5 text-sm font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-blue-100 transition-colors"
        >
          🎯 Select Element
        </button>
      </div>
    );
  }

  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 space-y-3">
      {/* Edit Mode Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
          <span className="text-sm font-medium text-blue-900">
            {isEditModeActive ? 'Element Selection Mode' : 'Element Selected'}
          </span>
        </div>
        <button
          onClick={onExitEditMode}
          className="px-2 py-1 text-xs font-medium text-red-600 bg-red-50 border border-red-200 rounded hover:bg-red-100 transition-colors"
        >
          ✕ Exit
        </button>
      </div>

      {/* Instructions */}
      {isEditModeActive && !selectedElement && (
        <div className="text-sm text-blue-700 bg-blue-100 rounded p-2">
          <p className="font-medium mb-1">📍 Click any element in the preview to select it for editing</p>
          <p className="text-xs text-blue-600">
            You can select buttons, text, cards, sections, or any other element
          </p>
        </div>
      )}

      {/* Selected Element Info */}
      {selectedElement && (
        <div className="space-y-2">
          <div className="text-sm text-blue-700 bg-blue-100 rounded p-2">
            <p className="font-medium mb-1">✅ Element Selected</p>
            <div className="text-xs space-y-1">
              <p><span className="font-medium">Tag:</span> {selectedElement.elementContext.tagName}</p>
              {selectedElement.elementContext.id && (
                <p><span className="font-medium">ID:</span> #{selectedElement.elementContext.id}</p>
              )}
              {selectedElement.elementContext.className && (
                <p><span className="font-medium">Classes:</span> {selectedElement.elementContext.className}</p>
              )}
              <p><span className="font-medium">Selector:</span> {selectedElement.selector}</p>
            </div>
          </div>

          {/* Target Position Options */}
          <div className="flex flex-wrap gap-1">
            <span className="text-xs text-blue-600 font-medium">Edit mode:</span>
            <button className="px-2 py-0.5 text-xs bg-blue-200 text-blue-800 rounded">
              Replace
            </button>
            <button className="px-2 py-0.5 text-xs bg-gray-100 text-gray-600 rounded hover:bg-blue-200 hover:text-blue-800">
              Before
            </button>
            <button className="px-2 py-0.5 text-xs bg-gray-100 text-gray-600 rounded hover:bg-blue-200 hover:text-blue-800">
              After
            </button>
            <button className="px-2 py-0.5 text-xs bg-gray-100 text-gray-600 rounded hover:bg-blue-200 hover:text-blue-800">
              Inside
            </button>
          </div>

          {/* Actions */}
          <div className="flex space-x-2">
            <button
              onClick={onClearSelection}
              className="px-2 py-1 text-xs font-medium text-gray-600 bg-gray-100 border border-gray-200 rounded hover:bg-gray-200 transition-colors"
            >
              Clear Selection
            </button>
            <button
              onClick={onToggleEditMode}
              className="px-2 py-1 text-xs font-medium text-blue-600 bg-blue-100 border border-blue-200 rounded hover:bg-blue-200 transition-colors"
            >
              Select Different Element
            </button>
          </div>
        </div>
      )}

      {/* Tips */}
      <div className="text-xs text-blue-600 border-t border-blue-200 pt-2">
        💡 <span className="font-medium">Tip:</span> Selected elements will be highlighted with a blue outline
      </div>
    </div>
  );
};

export default EditModeControls;
