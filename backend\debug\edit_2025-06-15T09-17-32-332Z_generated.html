<div id="app">
  <nav class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between h-16">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <h1 class="text-xl font-semibold text-gray-900 mr-8">SalesPro CRM</h1>
          </div>
          <div class="flex space-x-2">
            <button data-nav="dashboard" class="border-blue-500 text-gray-900 whitespace-nowrap py-2 px-4 mx-1 border-b-2 font-medium text-sm">Dashboard</button>
            <button data-nav="contacts" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-4 mx-1 border-b-2 font-medium text-sm transition-colors duration-200 rounded-t-lg hover:bg-gray-50">Contacts</button>
            <button data-nav="opportunities" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-4 mx-1 border-b-2 font-medium text-sm transition-colors duration-200 rounded-t-lg hover:bg-gray-50">Opportunities</button>
            <button data-nav="reports" class="border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-2 px-4 mx-1 border-b-2 font-medium text-sm transition-colors duration-200 rounded-t-lg hover:bg-gray-50">Reports</button>
          </div>
        </div>
        <div class="flex items-center">
          <div class="text-sm text-gray-500 mr-4">
            <i class="fas fa-user-circle mr-1"></i>
            John Smith
          </div>
          <button data-action="openModal" data-target="settingsModal" class="text-gray-400 hover:text-gray-500">
            <i class="fas fa-cog"></i>
          </button>
        </div>
      </div>
    </div>
  </nav>

  <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <section data-view="dashboard">
      <div class="mb-8">
        <h2 class="text-2xl font-bold text-gray-900">Dashboard Overview</h2>
        <p class="text-gray-500">Welcome back! Here's what's happening today.</p>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Stats Cards -->
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-500">Total Opportunities</p>
              <p class="text-3xl font-bold text-gray-900">24</p>
            </div>
            <div class="bg-blue-100 p-3 rounded-full">
              <i class="fas fa-briefcase text-blue-600"></i>
            </div>
          </div>
          <p class="text-sm text-green-600 mt-2">↑ 12% from last month</p>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-500">Closed Won</p>
              <p class="text-3xl font-bold text-gray-900">$128K</p>
            </div>
            <div class="bg-green-100 p-3 rounded-full">
              <i class="fas fa-check-circle text-green-600"></i>
            </div>
          </div>
          <p class="text-sm text-green-600 mt-2">↑ 8% from last month</p>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-500">New Contacts</p>
              <p class="text-3xl font-bold text-gray-900">14</p>
            </div>
            <div class="bg-purple-100 p-3 rounded-full">
              <i id="i-mbxg3vfj-f4sj" class="fas fa-user-plus text-purple-600" data-action="openModal" data-target="addContactModal"></i>
            </div>
          </div>
          <p class="text-sm text-red-600 mt-2">↓ 3% from last month</p>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-500">Pipeline Value</p>
              <p class="text-3xl font-bold text-gray-900">$420K</p>
            </div>
            <div class="bg-yellow-100 p-3 rounded-full">
              <i class="fas fa-chart-line text-yellow-600"></i>
            </div>
          </div>
          <p class="text-sm text-green-600 mt-2">↑ 15% from last month</p>
        </div>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- Pipeline Chart -->
        <div class="bg-white rounded-lg shadow p-6 lg:col-span-2">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Sales Pipeline</h3>
            <select class="border border-gray-300 rounded-md px-3 py-1 text-sm">
              <option>Last 30 Days</option>
              <option>Last Quarter</option>
              <option>Last Year</option>
            </select>
          </div>
          <div data-action="renderChart" data-chart-type="bar" data-chart-id="pipelineChart" class="h-64"></div>
        </div>

        <!-- Recent Activities -->
        <div class="bg-white rounded-lg shadow p-6">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-medium text-gray-900">Recent Activities</h3>
            <button data-action="openModal" data-target="addActivityModal" class="text-blue-600 text-sm font-medium">Add New</button>
          </div>
          <div class="space-y-4">
            <div class="flex items-start">
              <div class="bg-blue-100 p-2 rounded-full mr-3">
                <i class="fas fa-phone text-blue-600 text-sm"></i>
              </div>
              <div>
                <p class="text-sm font-medium">Call with Acme Corp</p>
                <p class="text-xs text-gray-500">Today, 10:30 AM</p>
                <p class="text-sm text-gray-600 mt-1">Discussed new product requirements</p>
              </div>
            </div>
            <div class="flex items-start">
              <div class="bg-green-100 p-2 rounded-full mr-3">
                <i class="fas fa-check text-green-600 text-sm"></i>
              </div>
              <div>
                <p class="text-sm font-medium">Closed deal with TechSolutions</p>
                <p class="text-xs text-gray-500">Yesterday, 3:45 PM</p>
                <p class="text-sm text-gray-600 mt-1">$25,000 annual contract</p>
              </div>
            </div>
            <div class="flex items-start">
              <div class="bg-purple-100 p-2 rounded-full mr-3">
                <i class="fas fa-envelope text-purple-600 text-sm"></i>
              </div>
              <div>
                <p class="text-sm font-medium">Sent proposal to Innovate Inc</p>
                <p class="text-xs text-gray-500">Yesterday, 11:20 AM</p>
                <p class="text-sm text-gray-600 mt-1">Custom software development</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Upcoming Tasks -->
      <div class="bg-white rounded-lg shadow p-6 mb-8">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-medium text-gray-900">Upcoming Tasks</h3>
          <button data-action="openModal" data-target="addTaskModal" class="text-blue-600 text-sm font-medium">Add Task</button>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Task</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Related To</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Due Date</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Priority</th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Follow up on proposal</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Innovate Inc</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Today</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">High</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <button data-action="openModal" data-target="editTaskModal" class="text-blue-600 hover:text-blue-900 mr-3">Edit</button>
                  <button class="text-green-600 hover:text-green-900">Complete</button>
                </td>
              </tr>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Schedule demo</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Global Tech</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Tomorrow</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Medium</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <button data-action="openModal" data-target="editTaskModal" class="text-blue-600 hover:text-blue-900 mr-3">Edit</button>
                  <button class="text-green-600 hover:text-green-900">Complete</button>
                </td>
              </tr>
              <tr>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">Prepare Q2 report</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">Internal</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">May 15</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Low</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <button data-action="openModal" data-target="editTaskModal" class="text-blue-600 hover:text-blue-900 mr-3">Edit</button>
                  <button class="text-green-600 hover:text-green-900">Complete</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Top Opportunities -->
      <div class="bg-white rounded-lg shadow p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-lg font-medium text-gray-900">Top Opportunities</h3>
          <button data-action="openModal" data-target="addOpportunityModal" class="text-blue-600 text-sm font-medium">Add Opportunity</button>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="flex justify-between items-start">
              <div>
                <h4 class="font-medium text-gray-900">Enterprise Software</h4>
                <p class="text-sm text-gray-500">TechSolutions Inc</p>
              </div>
              <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Closing</span>
            </div>
            <div class="mt-4">
              <p class="text-sm text-gray-600">$75,000</p>
              <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div class="bg-green-600 h-2 rounded-full" style="width: 90%"></div>
              </div>
              <p class="text-xs text-gray-500 mt-1">90% chance to close</p>
            </div>
            <div class="mt-4 flex justify-between items-center">
              <span class="text-sm text-gray-500">Expected close: May 30</span>
              <button data-action="openModal" data-target="viewOpportunityModal" class="text-blue-600 text-sm font-medium">View</button>
            </div>
          </div>
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="flex justify-between items-start">
              <div>
                <h4 class="font-medium text-gray-900">Marketing Suite</h4>
                <p class="text-sm text-gray-500">BrandVision</p>
              </div>
              <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">Proposal</span>
            </div>
            <div class="mt-4">
              <p class="text-sm text-gray-600">$42,000</p>
              <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div class="bg-blue-600 h-2 rounded-full" style="width: 60%"></div>
              </div>
              <p class="text-xs text-gray-500 mt-1">60% chance to close</p>
            </div>
            <div class="mt-4 flex justify-between items-center">
              <span class="text-sm text-gray-500">Expected close: Jun 15</span>
              <button data-action="openModal" data-target="viewOpportunityModal" class="text-blue-600 text-sm font-medium">View</button>
            </div>
          </div>
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="flex justify-between items-start">
              <div>
                <h4 class="font-medium text-gray-900">HR Platform</h4>
                <p class="text-sm text-gray-500">PeopleFirst</p>
              </div>
              <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">Discovery</span>
            </div>
            <div class="mt-4">
              <p class="text-sm text-gray-600">$120,000</p>
              <div class="w-full bg-gray-200 rounded-full h-2 mt-2">
                <div class="bg-yellow-500 h-2 rounded-full" style="width: 30%"></div>
              </div>
              <p class="text-xs text-gray-500 mt-1">30% chance to close</p>
            </div>
            <div class="mt-4 flex justify-between items-center">
              <span class="text-sm text-gray-500">Expected close: Jul 1</span>
              <button data-action="openModal" data-target="viewOpportunityModal" class="text-blue-600 text-sm font-medium">View</button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Other Views (Hidden by default) -->
    <section data-view="contacts" class="hidden">
      <!-- Contacts view content would go here -->
    </section>
    <section data-view="opportunities" class="hidden">
      <!-- Opportunities view content would go here -->
    </section>
    <section data-view="reports" class="hidden">
      <!-- Reports view content would go here -->
    </section>
  </main>

  <!-- Add Contact Modal -->
  <div id="addContactModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium text-gray-900">Add New Contact</h3>
        <button data-action="closeModal" class="text-gray-400 hover:text-gray-500">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <form id="contactForm">
        <div class="mb-4">
          <label for="contactName" class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
          <input type="text" id="contactName" required class="w-full border border-gray-300 rounded-md px-3 py-2">
          <p class="text-xs text-red-600 hidden mt-1" id="nameError">Please enter a valid name</p>
        </div>
        <div class="mb-4">
          <label for="contactEmail" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
          <input type="email" id="contactEmail" required class="w-full border border-gray-300 rounded-md px-3 py-2">
          <p class="text-xs text-red-600 hidden mt-1" id="emailError">Please enter a valid email</p>
        </div>
        <div class="mb-4">
          <label for="contactPhone" class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
          <input type="tel" id="contactPhone" class="w-full border border-gray-300 rounded-md px-3 py-2">
        </div>
        <div class="mb-4">
          <label for="contactCompany" class="block text-sm font-medium text-gray-700 mb-1">Company</label>
          <input type="text" id="contactCompany" class="w-full border border-gray-300 rounded-md px-3 py-2">
        </div>
        <div class="mb-4">
          <label for="contactTitle" class="block text-sm font-medium text-gray-700 mb-1">Title</label>
          <input type="text" id="contactTitle" class="w-full border border-gray-300 rounded-md px-3 py-2">
        </div>
        <div class="flex justify-end space-x-3">
          <button data-action="closeModal" type="button" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">Cancel</button>
          <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700">Save Contact</button>
        </div>
      </form>
    </div>
  </div>

  <!-- Other Modals -->
  <div id="addActivityModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium text-gray-900">Add New Activity</h3>
        <button data-action="closeModal" class="text-gray-400 hover:text-gray-500">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <form>
        <div class="mb-4">
          <label for="activityType" class="block text-sm font-medium text-gray-700 mb-1">Activity Type</label>
          <select id="activityType" class="w-full border border-gray-300 rounded-md px-3 py-2">
            <option>Call</option>
            <option>Email</option>
            <option>Meeting</option>
            <option>Task</option>
          </select>
        </div>
        <div class="mb-4">
          <label for="activitySubject" class="block text-sm font-medium text-gray-700 mb-1">Subject</label>
          <input type="text" id="activitySubject" class="w-full border border-gray-300 rounded-md px-3 py-2">
        </div>
        <div class="mb-4">
          <label for="activityRelatedTo" class="block text-sm font-medium text-gray-700 mb-1">Related To</label>
          <input type="text" id="activityRelatedTo" class="w-full border border-gray-300 rounded-md px-3 py-2">
        </div>
        <div class="mb-4">
          <label for="activityDate" class="block text-sm font-medium text-gray-700 mb-1">Date & Time</label>
          <input type="datetime-local" id="activityDate" class="w-full border border-gray-300 rounded-md px-3 py-2">
        </div>
        <div class="mb-4">
          <label for="activityNotes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
          <textarea id="activityNotes" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2"></textarea>
        </div>
        <div class="flex justify-end space-x-3">
          <button data-action="closeModal" type="button" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">Cancel</button>
          <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">Save Activity</button>
        </div>
      </form>
    </div>
  </div>

  <div id="addTaskModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium text-gray-900">Add New Task</h3>
        <button data-action="closeModal" class="text-gray-400 hover:text-gray-500">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <form>
        <div class="mb-4">
          <label for="taskSubject" class="block text-sm font-medium text-gray-700 mb-1">Task</label>
          <input type="text" id="taskSubject" class="w-full border border-gray-300 rounded-md px-3 py-2">
        </div>
        <div class="mb-4">
          <label for="taskRelatedTo" class="block text-sm font-medium text-gray-700 mb-1">Related To</label>
          <input type="text" id="taskRelatedTo" class="w-full border border-gray-300 rounded-md px-3 py-2">
        </div>
        <div class="mb-4">
          <label for="taskDueDate" class="block text-sm font-medium text-gray-700 mb-1">Due Date</label>
          <input type="date" id="taskDueDate" class="w-full border border-gray-300 rounded-md px-3 py-2">
        </div>
        <div class="mb-4">
          <label for="taskPriority" class="block text-sm font-medium text-gray-700 mb-1">Priority</label>
          <select id="taskPriority" class="w-full border border-gray-300 rounded-md px-3 py-2">
            <option>Low</option>
            <option>Medium</option>
            <option>High</option>
          </select>
        </div>
        <div class="mb-4">
          <label for="taskNotes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
          <textarea id="taskNotes" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2"></textarea>
        </div>
        <div class="flex justify-end space-x-3">
          <button data-action="closeModal" type="button" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">Cancel</button>
          <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">Save Task</button>
        </div>
      </form>
    </div>
  </div>

  <div id="editTaskModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium text-gray-900">Edit Task</h3>
        <button data-action="closeModal" class="text-gray-400 hover:text-gray-500">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <form>
        <div class="mb-4">
          <label for="editTaskSubject" class="block text-sm font-medium text-gray-700 mb-1">Task</label>
          <input type="text" id="editTaskSubject" value="Follow up on proposal" class="w-full border border-gray-300 rounded-md px-3 py-2">
        </div>
        <div class="mb-4">
          <label for="editTaskRelatedTo" class="block text-sm font-medium text-gray-700 mb-1">Related To</label>
          <input type="text" id="editTaskRelatedTo" value="Innovate Inc" class="w-full border border-gray-300 rounded-md px-3 py-2">
        </div>
        <div class="mb-4">
          <label for="editTaskDueDate" class="block text-sm font-medium text-gray-700 mb-1">Due Date</label>
          <input type="date" id="editTaskDueDate" value="2023-05-10" class="w-full border border-gray-300 rounded-md px-3 py-2">
        </div>
        <div class="mb-4">
          <label for="editTaskPriority" class="block text-sm font-medium text-gray-700 mb-1">Priority</label>
          <select id="editTaskPriority" class="w-full border border-gray-300 rounded-md px-3 py-2">
            <option>Low</option>
            <option>Medium</option>
            <option selected>High</option>
          </select>
        </div>
        <div class="mb-4">
          <label for="editTaskNotes" class="block text-sm font-medium text-gray-700 mb-1">Notes</label>
          <textarea id="editTaskNotes" rows="3" class="w-full border border-gray-300 rounded-md px-3 py-2">Client requested additional features in the proposal. Need to follow up on pricing.</textarea>
        </div>
        <div class="flex justify-end space-x-3">
          <button data-action="closeModal" type="button" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">Cancel</button>
          <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">Update Task</button>
        </div>
      </form>
    </div>
  </div>

  <div id="addOpportunityModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium text-gray-900">Add New Opportunity</h3>
        <button data-action="closeModal" class="text-gray-400 hover:text-gray-500">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <form>
        <div class="mb-4">
          <label for="opportunityName" class="block text-sm font-medium text-gray-700 mb-1">Opportunity Name</label>
          <input type="text" id="opportunityName" class="w-full border border-gray-300 rounded-md px-3 py-2">
        </div>
        <div class="mb-4">
          <label for="opportunityAccount" class="block text-sm font-medium text-gray-700 mb-1">Account</label>
          <input type="text" id="opportunityAccount" class="w-full border border-gray-300 rounded-md px-3 py-2">
        </div>
        <div class="mb-4">
          <label for="opportunityAmount" class="block text-sm font-medium text-gray-700 mb-1">Amount ($)</label>
          <input type="number" id="opportunityAmount" class="w-full border border-gray-300 rounded-md px-3 py-2">
        </div>
        <div class="mb-4">
          <label for="opportunityStage" class="block text-sm font-medium text-gray-700 mb-1">Stage</label>
          <select id="opportunityStage" class="w-full border border-gray-300 rounded-md px-3 py-2">
            <option>Prospecting</option>
            <option>Discovery</option>
            <option>Proposal</option>
            <option>Negotiation</option>
            <option>Closing</option>
          </select>
        </div>
        <div class="mb-4">
          <label for="opportunityCloseDate" class="block text-sm font-medium text-gray-700 mb-1">Expected Close Date</label>
          <input type="date" id="opportunityCloseDate" class="w-full border border-gray-300 rounded-md px-3 py-2">
        </div>
        <div class="flex justify-end space-x-3">
          <button data-action="closeModal" type="button" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">Cancel</button>
          <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700">Save Opportunity</button>
        </div>
      </form>
    </div>
  </div>

  <div id="viewOpportunityModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium text-gray-900">Opportunity Details</h3>
        <button data-action="closeModal" class="text-gray-400 hover:text-gray-500">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="mb-6">
        <h4 class="font-bold text-gray-900">Enterprise Software</h4>
        <p class="text-sm text-gray-500">TechSolutions Inc</p>
      </div>
      <div class="space-y-4">
        <div>
          <p class="text-sm font-medium text-gray-500">Amount</p>
          <p class="text-gray-900">$75,000</p>
        </div>
        <div>
          <p class="text-sm font-medium text-gray-500">Stage</p>
          <p class="text-gray-900">Closing</p>
        </div>
        <div>
          <p class="text-sm font-medium text-gray-500">Probability</p>
          <div class="w-full bg-gray-200 rounded-full h-2 mt-1">
            <div class="bg-green-600 h-2 rounded-full" style="width: 90%"></div>
          </div>
          <p class="text-xs text-gray-500 mt-1">90% chance to close</p>
        </div>
        <div>
          <p class="text-sm font-medium text-gray-500">Expected Close Date</p>
          <p class="text-gray-900">May 30, 2023</p>
        </div>
        <div>
          <p class="text-sm font-medium text-gray-500">Owner</p>
          <p class="text-gray-900">John Smith</p>
        </div>
        <div>
          <p class="text-sm font-medium text-gray-500">Description</p>
          <p class="text-gray-900">Enterprise software solution for managing global operations with custom reporting features.</p>
        </div>
      </div>
      <div class="mt-6 flex justify-end">
        <button data-action="closeModal" type="button" class="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">Close</button>
      </div>
    </div>
  </div>

  <div id="settingsModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium text-gray-900">Settings</h3>
        <button data-action="closeModal" class="text-gray-400 hover:text-gray-500">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="space-y-4">
        <div>
          <p class="text-sm font-medium text-gray-700 mb-1">Theme</p>
          <div class="flex space-x-2">
            <button class="w-8 h-8 rounded-full bg-blue-600 border-2 border-blue-700"></button>
            <button class="w-8 h-8 rounded-full bg-green-600 border-2 border-transparent"></button>