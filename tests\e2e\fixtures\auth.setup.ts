import { test as setup, expect } from '@playwright/test';
import { AuthPage } from '../pages/auth.page';

/**
 * Authentication setup for E2E tests
 * 
 * This setup file handles authentication once and saves the state
 * to be reused across all test files, avoiding repeated login flows.
 */

const authFile = '../reports/auth-state.json';

setup('authenticate', async ({ page }) => {
  console.log('🔐 Setting up authentication for E2E tests...');
  
  const authPage = new AuthPage(page);
  
  try {
    // Navigate to the application
    await page.goto('/');
    
    // Check if already authenticated (in case of existing session)
    const isAlreadyAuthenticated = await authPage.isAuthenticated();
    
    if (isAlreadyAuthenticated) {
      console.log('✅ Already authenticated, saving current state');
      await page.context().storageState({ path: authFile });
      return;
    }
    
    // Perform authentication
    console.log('🚀 Starting authentication flow...');
    
    // Method 1: Try direct authentication bypass (for testing)
    if (process.env.TEST_ENVIRONMENT === 'local') {
      const bypassSuccess = await authPage.bypassAuthForTesting();
      if (bypassSuccess) {
        console.log('✅ Authentication bypassed for testing');
        await page.context().storageState({ path: authFile });
        return;
      }
    }
    
    // Method 2: Google OAuth flow (for staging/production)
    await authPage.clickSignIn();
    
    // Handle Google OAuth
    if (process.env.GOOGLE_TEST_EMAIL && process.env.GOOGLE_TEST_PASSWORD) {
      await authPage.handleGoogleOAuth(
        process.env.GOOGLE_TEST_EMAIL,
        process.env.GOOGLE_TEST_PASSWORD
      );
    } else {
      // Wait for manual authentication in headed mode
      console.log('⏳ Waiting for manual authentication...');
      await authPage.waitForAuthentication();
    }
    
    // Verify authentication was successful
    await expect(page).toHaveURL(/\/prototypes/);
    
    // Verify user is authenticated
    const userInfo = await authPage.getUserInfo();
    expect(userInfo).toBeTruthy();
    console.log(`✅ Authenticated as: ${userInfo?.email || 'Unknown'}`);
    
    // Save authentication state
    await page.context().storageState({ path: authFile });
    console.log('💾 Authentication state saved');
    
  } catch (error) {
    console.error('❌ Authentication setup failed:', error);
    
    // Take screenshot for debugging
    await page.screenshot({ 
      path: '../reports/auth-setup-failure.png',
      fullPage: true 
    });
    
    throw error;
  }
});

setup('verify auth state', async ({ page }) => {
  console.log('🔍 Verifying saved authentication state...');
  
  try {
    // Load the saved auth state
    await page.goto('/prototypes');
    
    // Verify we're still authenticated
    const authPage = new AuthPage(page);
    const isAuthenticated = await authPage.isAuthenticated();
    
    expect(isAuthenticated).toBe(true);
    console.log('✅ Authentication state verified');
    
  } catch (error) {
    console.error('❌ Authentication state verification failed:', error);
    throw error;
  }
});

/**
 * Cleanup function to reset authentication state if needed
 */
setup('cleanup auth', async ({ page }) => {
  // This setup runs after all tests to clean up if needed
  console.log('🧹 Cleaning up authentication state...');
  
  try {
    // Only run cleanup if explicitly requested
    if (process.env.CLEANUP_AUTH === 'true') {
      const authPage = new AuthPage(page);
      await authPage.logout();
      console.log('✅ Authentication cleaned up');
    }
  } catch (error) {
    console.warn('⚠️ Auth cleanup failed (non-critical):', error);
  }
});
