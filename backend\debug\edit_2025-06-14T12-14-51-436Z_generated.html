<div id="app">
    <h1 class="text-xl font-semibold text-gray-900 mr-8" onclick="toggleAddAccountModal()">Add Account</h1>
    <div id="addAccountModal" class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3 text-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900">Add New Account</h3>
                <div class="mt-2 px-7 py-3">
                    <p class="text-sm text-gray-500">Account creation form would go here</p>
                </div>
                <div class="items-center px-4 py-3">
                    <button onclick="toggleAddAccountModal()" class="px-4 py-2 bg-blue-500 text-white text-base font-medium rounded-md shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-300">
                        Close
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    function toggleAddAccountModal() {
        try {
            const modal = document.getElementById('addAccountModal');
            if (!modal) throw new Error('Modal element not found');
            
            modal.classList.toggle('hidden');
            modal.classList.toggle('block');
        } catch (error) {
            console.error('Error toggling modal:', error);
            alert('An error occurred while trying to open the account creation form. Please try again.');
        }
    }
</script>