import { Page, Locator, expect } from '@playwright/test';
import { BasePage } from './base.page';

/**
 * V3 Editor Page Object Model
 * 
 * Handles all interactions with the V3 Editor including:
 * - Prototype generation
 * - Iterative editing
 * - Streaming response handling
 * - Preview and code view
 * - Conversation management
 */
export class EditorPage extends BasePage {
  
  // Main editor selectors
  private readonly promptInput = '[data-testid="prompt-input"], textarea[placeholder*="prompt"], textarea[placeholder*="describe"]';
  private readonly generateButton = 'button:has-text("Generate"), button[type="submit"]';
  private readonly sendButton = 'button:has-text("Send"), button[aria-label="Send"]';
  
  // Preview and code view
  private readonly previewPanel = '[data-testid="preview-panel"], .preview-panel, iframe';
  private readonly codeView = '[data-testid="code-view"], .code-view';
  private readonly viewToggle = '[data-testid="view-toggle"], button:has-text("Code"), button:has-text("Preview")';
  
  // Chat interface
  private readonly chatContainer = '[data-testid="chat-container"], .chat-container, .messages';
  private readonly messageList = '[data-testid="message-list"], .message-list';
  private readonly userMessage = '[data-testid="user-message"], .user-message';
  private readonly assistantMessage = '[data-testid="assistant-message"], .assistant-message';
  
  // Loading and streaming indicators
  private readonly loadingIndicator = '[data-testid="loading"], .loading, .spinner';
  private readonly streamingIndicator = '[data-testid="streaming"], .streaming';
  private readonly generatingText = 'text=Generating, text=Loading, text=Processing';
  
  // Page management
  private readonly pageManager = '[data-testid="page-manager"], .page-manager';
  private readonly addPageButton = 'button:has-text("Add Page"), [data-testid="add-page"]';
  private readonly pageList = '[data-testid="page-list"], .page-list';
  
  // Navigation and header
  private readonly editorHeader = '[data-testid="editor-header"], .editor-header';
  private readonly backButton = 'button:has-text("Back"), [data-testid="back-button"]';
  private readonly saveButton = 'button:has-text("Save"), [data-testid="save-button"]';
  
  constructor(page: Page) {
    super(page);
  }
  
  /**
   * Navigate to the V3 Editor
   */
  async navigateToEditor() {
    console.log('🎯 Navigating to V3 Editor...');
    await this.goto('/editor-v3-refactored');
    await this.waitForEditorLoad();
  }
  
  /**
   * Wait for editor to fully load
   */
  async waitForEditorLoad() {
    console.log('⏳ Waiting for editor to load...');
    
    // Wait for main editor elements
    await this.waitForElement(this.promptInput);
    await this.waitForLoadingComplete();
    
    // Ensure preview panel is ready
    try {
      await this.waitForElement(this.previewPanel, 10000);
    } catch {
      console.log('Preview panel not immediately visible (may load after first generation)');
    }
    
    console.log('✅ Editor loaded successfully');
  }
  
  /**
   * Enter a prompt in the input field
   */
  async enterPrompt(prompt: string) {
    console.log(`📝 Entering prompt: "${prompt.substring(0, 50)}..."`);
    
    await this.waitForElement(this.promptInput);
    await this.page.fill(this.promptInput, prompt);
    
    // Wait a moment for any input validation
    await this.page.waitForTimeout(500);
  }
  
  /**
   * Click the generate button to start prototype generation
   */
  async clickGenerate() {
    console.log('🚀 Clicking Generate button...');
    
    // Try different possible generate button selectors
    const generateSelectors = [this.generateButton, this.sendButton];
    
    for (const selector of generateSelectors) {
      if (await this.isElementVisible(selector)) {
        await this.page.click(selector);
        return;
      }
    }
    
    throw new Error('Generate button not found');
  }
  
  /**
   * Generate a prototype from a prompt (complete flow)
   */
  async generatePrototype(prompt: string) {
    console.log(`🎨 Generating prototype with prompt: "${prompt}"`);
    
    await this.enterPrompt(prompt);
    await this.clickGenerate();
    await this.waitForGeneration();
    
    console.log('✅ Prototype generation completed');
  }
  
  /**
   * Wait for prototype generation to complete
   */
  async waitForGeneration(timeout: number = 5 * 60 * 1000) {
    console.log('⏳ Waiting for prototype generation...');
    
    // Wait for generation to start
    await this.waitForGenerationStart();
    
    // Wait for generation to complete
    await this.waitForGenerationComplete(timeout);
    
    console.log('✅ Generation completed successfully');
  }
  
  /**
   * Wait for generation to start (loading indicators appear)
   */
  async waitForGenerationStart(timeout: number = 10000) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      // Check for loading indicators
      if (await this.isElementVisible(this.loadingIndicator) ||
          await this.isElementVisible(this.streamingIndicator) ||
          await this.isElementVisible(this.generatingText)) {
        console.log('🔄 Generation started');
        return;
      }
      
      await this.page.waitForTimeout(500);
    }
    
    console.warn('⚠️ Generation start indicators not detected');
  }
  
  /**
   * Wait for generation to complete (loading indicators disappear)
   */
  async waitForGenerationComplete(timeout: number = 5 * 60 * 1000) {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      // Check if loading indicators are gone
      const isLoading = await this.isElementVisible(this.loadingIndicator) ||
                       await this.isElementVisible(this.streamingIndicator) ||
                       await this.isElementVisible(this.generatingText);
      
      if (!isLoading) {
        // Wait a bit more to ensure content is fully rendered
        await this.page.waitForTimeout(2000);
        console.log('✅ Generation completed');
        return;
      }
      
      await this.page.waitForTimeout(1000);
    }
    
    throw new Error('Generation timeout - took longer than expected');
  }
  
  /**
   * Send a follow-up prompt for iterative editing
   */
  async sendFollowUpPrompt(prompt: string) {
    console.log(`✏️ Sending follow-up prompt: "${prompt}"`);
    
    await this.enterPrompt(prompt);
    await this.clickGenerate();
    await this.waitForGeneration();
    
    console.log('✅ Follow-up edit completed');
  }
  
  /**
   * Get the generated HTML content
   */
  async getGeneratedHTML(): Promise<string> {
    console.log('📄 Retrieving generated HTML...');
    
    try {
      // Method 1: Try to get from code view
      if (await this.isElementVisible(this.codeView)) {
        const htmlContent = await this.page.textContent(this.codeView);
        if (htmlContent && htmlContent.trim()) {
          return htmlContent;
        }
      }
      
      // Method 2: Try to get from preview iframe
      if (await this.isElementVisible(this.previewPanel)) {
        const iframe = this.page.locator('iframe').first();
        if (await iframe.isVisible()) {
          const htmlContent = await iframe.evaluate((frame) => {
            return frame.contentDocument?.documentElement.outerHTML || '';
          });
          if (htmlContent) {
            return htmlContent;
          }
        }
      }
      
      // Method 3: Try to get from page state/localStorage
      const htmlFromStorage = await this.page.evaluate(() => {
        return localStorage.getItem('currentHTML') || 
               localStorage.getItem('htmlContent') || 
               '';
      });
      
      if (htmlFromStorage) {
        return htmlFromStorage;
      }
      
      throw new Error('Could not retrieve generated HTML content');
      
    } catch (error) {
      console.error('❌ Failed to get generated HTML:', error);
      throw error;
    }
  }
  
  /**
   * Switch between preview and code view
   */
  async switchView(view: 'preview' | 'code') {
    console.log(`🔄 Switching to ${view} view...`);
    
    if (await this.isElementVisible(this.viewToggle)) {
      const toggleButton = this.page.locator(this.viewToggle, { hasText: view === 'preview' ? 'Preview' : 'Code' });
      if (await toggleButton.isVisible()) {
        await toggleButton.click();
        await this.page.waitForTimeout(1000); // Wait for view to switch
      }
    }
  }
  
  /**
   * Verify the preview is displaying content
   */
  async verifyPreviewContent(): Promise<boolean> {
    console.log('🔍 Verifying preview content...');
    
    try {
      await this.waitForElement(this.previewPanel);
      
      // Check if iframe has content
      const iframe = this.page.locator('iframe').first();
      if (await iframe.isVisible()) {
        const hasContent = await iframe.evaluate((frame) => {
          const doc = frame.contentDocument;
          return doc && doc.body && doc.body.innerHTML.trim().length > 0;
        });
        
        if (hasContent) {
          console.log('✅ Preview content verified');
          return true;
        }
      }
      
      console.log('❌ Preview content not found');
      return false;
      
    } catch (error) {
      console.error('❌ Preview verification failed:', error);
      return false;
    }
  }
  
  /**
   * Get conversation history
   */
  async getConversationHistory(): Promise<Array<{ role: string; content: string }>> {
    console.log('💬 Retrieving conversation history...');
    
    try {
      const messages = await this.page.locator(`${this.userMessage}, ${this.assistantMessage}`).all();
      const conversation = [];
      
      for (const message of messages) {
        const isUser = await message.locator(this.userMessage).count() > 0;
        const content = await message.textContent() || '';
        
        conversation.push({
          role: isUser ? 'user' : 'assistant',
          content: content.trim()
        });
      }
      
      return conversation;
      
    } catch (error) {
      console.error('❌ Failed to get conversation history:', error);
      return [];
    }
  }
  
  /**
   * Verify that a specific message appears in the conversation
   */
  async verifyMessageInConversation(messageText: string, role: 'user' | 'assistant' = 'user') {
    console.log(`🔍 Verifying ${role} message: "${messageText}"`);
    
    const messageSelector = role === 'user' ? this.userMessage : this.assistantMessage;
    const messageElement = this.page.locator(messageSelector, { hasText: messageText });
    
    await expect(messageElement).toBeVisible({ timeout: 10000 });
    console.log('✅ Message verified in conversation');
  }
}
